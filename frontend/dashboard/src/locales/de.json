{"languageName": "De<PERSON>ch", "sharedButtons": {"create": "<PERSON><PERSON><PERSON><PERSON>", "save": "Speichern", "close": "Schließen", "copy": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "settings": "Einstellungen", "login": "Anmelden", "logout": "Abmelden", "edit": "<PERSON><PERSON><PERSON>", "select": "Auswählen", "cancel": "Abbrechen", "confirm": "Bestätigen", "saveSettings": "Einstellungen speichern", "setDefaultSettings": "Standardeinstellungen festlegen", "goToDashboard": "Zur Übersicht gehen", "add": "Hinzufügen", "send": "Senden", "next": "Nächste", "previous": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Z<PERSON>ücksetzen", "popout": "Popout in neuem Fenster öffnen"}, "sharedTexts": {"saved": "Erfolgreich gespeichert", "errorOnSave": "<PERSON><PERSON> beim <PERSON> von {error}", "enabled": "Aktiviert", "status": "Status", "name": "Name", "actions": "Aktionen", "responses": "Antworten", "reply": {"label": "Antworten", "text": "<PERSON>t wird Antworten auf Befehle als Antwort senden"}, "response": "Antwort", "user": "<PERSON><PERSON><PERSON>", "userName": "<PERSON><PERSON><PERSON><PERSON>", "dangerZone": "Gefahrenzone", "deleted": "Gelöscht", "updated": "<PERSON>ktual<PERSON><PERSON>", "disabled": "Deaktiviert", "setDefaultSettings": "Sind Sie sich sicher, dass Sie die Standardwerte wiederherstellen möchten?", "placeCursorMessage": "Platziere den Cursor nach \"$\" um die Variable zu ändern", "pagination": "{page} Seite(n) / {total} Gegenstand(e)", "paginationPerPage": "Pro Seite", "searchPlaceholder": "Suche...", "messages": "Nachrichten", "asc": "Aufstieg", "desc": "Absteigend", "hide": "Ausblenden", "view": "Anzeigen", "toggleColumns": "Spalten umschalten", "copied": "<PERSON><PERSON><PERSON>", "userSelected": "{count} <PERSON><PERSON><PERSON> ausgewählt", "userSelectPlaceholder": "Wähle einen Benutzer|Wähle mehrere Benutzer", "userNotFound": "<PERSON><PERSON> gefunden", "settings": "Einstellungen", "create": "<PERSON><PERSON><PERSON>", "edit": "Bearbei<PERSON>", "noData": "<PERSON><PERSON> ve<PERSON>ü<PERSON>"}, "sidebar": {"dashboard": "Übersicht", "commands": {"label": "<PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtin": "Integrier<PERSON>"}, "integrations": "Integrationen", "events": "Events", "chatAlerts": "Chatbenachrichtigungen", "overlays": "<PERSON><PERSON><PERSON>", "overlaysRegistry": "Ebenen registrieren", "songRequests": "Songanfragen", "timers": "Alarme", "roles": "Berechtigungen", "keywords": "Schlagwörter", "variables": "Variablen", "greetings": "Begrüßung", "alerts": "Warnungen", "games": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON>", "moderation": "Moderation", "publicPage": "Öffentliche Seite", "import": "Importieren", "community": "Community", "logout": "Ausloggen", "loggedAs": "<PERSON><PERSON><PERSON><PERSON> als", "lang": "<PERSON><PERSON><PERSON>", "darkTheme": "<PERSON><PERSON><PERSON> Design", "lightTheme": "<PERSON><PERSON> Design", "notifications": "Benachrichtigungen"}, "deleteConfirmation": {"text": "B<PERSON> du sicher, dass du das löschen möchtest?", "cancel": "Abbrechen", "confirm": "Bestätigen"}, "haveNoAccess": {"title": "<PERSON><PERSON>", "description": "Du bist nicht berechtigt diese Seite zu sehen, frage den Streamer nach einer Bereitstellung."}, "pageNotFound": {"title": "404 Nicht gefunden", "description": "<PERSON><PERSON> wissen, dass das Leben immer lächerlich ist."}, "commands": {"name": "Befehl | Befehle", "searchPlaceholder": "<PERSON><PERSON><PERSON> suchen...", "newCommandTitle": "<PERSON><PERSON><PERSON>", "importCommands": "Befehle importieren", "groups": {"manageButton": "Gruppen verwalten", "name": "Name", "color": "Farbe"}, "table": {"responses": {"empty": "Die Antworten sind leer!"}}, "modal": {"name": {"label": "Name", "validations": {"empty": "Befehlsname ist erforderlich.", "startsWith": "Befehlsname sollte nicht mit '!' beginnen.", "len": "Der Befehlsname sollte weniger als 25 Zeichen lang sein."}}, "aliases": {"label": "Zusatznamen"}, "gameCategories": {"label": "Aktiviere Befehl nur in bestimmten Kategorien (leer lassen für Deaktivierung)"}, "expiration": {"label": "<PERSON><PERSON><PERSON>", "actionsLabel": "Aktion nach Ablauf", "defaultWarning": "Ablaufdatum kann nicht für Standardbefehle gesetzt werden", "actions": {"delete": "Be<PERSON><PERSON> wird gel<PERSON>t", "disable": "<PERSON><PERSON><PERSON> wird de<PERSON>t"}}, "description": {"label": "Befehlsbeschreibung"}, "responses": {"add": "Antwort hinzufügen", "description": "Dies ist Ihre Befehlsantwort. Sie können Befehlsvariablen wie {0} verwenden.", "validations": {"empty": "Die Antwort darf nicht leer sein. Der Befehl kann ohne Antwort erstellt werden, so dass Sie dieses Feld überspringen können.", "len": "Die Antwort sollte weniger als 500 Zeichen lang sein."}, "defaultWarning": "Antworten können für Standardbefehle nicht bearbeitet werden."}, "permissions": {"divider": "Berechtigungen", "name": "<PERSON><PERSON>", "placeholder": "Wählen Sie Rollen aus, um Berechtigungen zur Verwendung von Be<PERSON> zu gewähren", "deniedUsers": "<PERSON><PERSON><PERSON>, die diesen Befehl nicht verwenden können.", "allowedUsers": "<PERSON><PERSON><PERSON>, die die Rollenbeschränkung für diesen Befehl umgehen können.", "exceptions": "Ausnahmen", "blocked": "<PERSON><PERSON><PERSON>"}, "restrictions": {"name": "Einschränkungen nach Statistiken", "watchTime": "Benötigte Beobachtungszeit um diesen Befehl zu verwenden.", "messages": "Benötigte Nachrichtenanzahl, um diesen Befehl zu verwenden.", "channelsPoints": "Benötigte Anzahl an ausgegebenen Kanalpunkten, um diesen Befehl zu verwenden."}, "cooldown": {"label": "Abklingzeit", "value": "Abklingzeit in Sekunden", "type": {"name": "Abklingzeit pro", "global": "Global", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pro Benutzer)"}}, "settings": {"divider": "Einstellungen", "visible": {"label": "Sichtbarkeit", "text": "Sichtbarkeit von Befehlen auf einer öffentlichen Befehlslistenseite."}, "keepOrder": {"label": "Ordnung halten", "text": "Der Bot behält diese Reihenfolge der Antworten bei, wenn er sie in den Chat sendet."}, "onlineOnly": {"label": "<PERSON>ur wenn Online", "text": "Dieser Befehl funktioniert nur, wenn der Stream online ist."}, "other": {"divider": "Sonstiges", "commandGroup": "Befehlsgruppe"}}, "alert": {"label": "<PERSON><PERSON><PERSON>en Si<PERSON> eine Warnung, die ausgelöst wird, wenn der Befehl verwendet wird."}}}, "dashboard": {"header": {"managingUser": "<PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "channelsAccess": "Kan<PERSON><PERSON> auf die Sie Zugriff haben"}, "botManage": {"title": "Bot-Status", "success": "Aktiviert und Moderator Status", "notEnabledTitle": "Bot ist im Kanal nicht aktiviert", "notModerator": "Wir haben festgestellt, dass der Bot kein Moderator auf diesem Kanal ist. Bitte verwenden Sie {0}, sonst wird der Bot keine Nachrichten im Chat senden und die meisten Funktionen funktionieren nicht", "leave": "Deaktivieren", "join": "Aktivieren"}, "statsWidgets": {"streamInfo": {"noTitle": "Stream-Titel kann nicht abgerufen werden!", "noCategory": "<PERSON><PERSON><PERSON> kann nicht abgerufen werden!", "modalTitle": "Stream Information bearbeiten", "title": "Stream-Titel", "category": "<PERSON><PERSON><PERSON>"}, "uptime": "Laufzeit", "followers": "Follower", "messages": "Nachrichten", "subs": "Abonnenten", "usedEmotes": "Gese<PERSON><PERSON>", "viewers": "<PERSON><PERSON><PERSON><PERSON>", "requestedSongs": "Angeforderte Songs"}, "widgets": {"chat": {"title": "Cha<PERSON>"}, "stream": {"title": "Stream"}, "events": {"title": "Events-Liste"}, "bot": {"title": "Bot-<PERSON>erwaltung"}, "audit-logs": {"title": "Audit-Logs", "search-label": "<PERSON><PERSON> nach", "search": {"channel": "<PERSON><PERSON>", "actor": "Akteur"}, "operation-type-label": "Operationstyp", "operation-type": {"create": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Gelöscht", "update": "Bearbeitet"}, "systems-label": "Systeme", "systems": {"badge": "Badge", "badge-user": "Benutzer Abzeichen", "channel-command": "<PERSON><PERSON><PERSON>", "channel-command-group": "Befehlsgruppe", "channel-variable": "Variabel", "channel-games-eight-ball": "8ball", "channel-games-duel": "<PERSON>ll", "channel-games-russian-roulette": "Russisches Roulette", "channel-games-seppuku": "Seppuku", "channel-games-voteban": "Voteban", "channel-greeting": "Begrüßung", "channel-keyword": "Schlüsselwort", "channel-moderation-setting": "Moderations-Einstellung", "channel-overlay-chat": "<PERSON><PERSON>", "channel-overlay-dudes": "Dudes Overlay", "channel-overlay-now-playing": "Now Playing Overlay", "channel-roles": "<PERSON><PERSON>", "channel-timers": "Timer", "channel-song-requests": "Song Requests", "channel-integrations": "Integration", "channel-alerts": "<PERSON><PERSON>", "channel-chat-alerts": "<PERSON><PERSON>"}}}}, "integrations": {"notLoggedIn": "Sie sind nicht angemeldet!", "songServicesInfo": "Durch die Bereitstellung des Zugriffs auf diesen Service kann Twir den aktuellen Song automatisch in den Chat laden und abspielen, wenn der Befehl {0} benutzt wird.", "donateServicesInfo": "Der Zugriff auf diesen Service ermöglicht es Twir, e<PERSON>hende Spenden zu hören, und wird Interaktionen für Sie in {0}, {1} <PERSON><PERSON><PERSON> e<PERSON>.", "discord": {"description": "Wenn du Zugriff auf Discord gewährst, kann Twir automatische Pings und Warnungen über den Stream-Status auf deinen Discord Servern einrichten.", "guildPluralization": "Keine Server | 1 Server | {count} Server", "connectedGuilds": "{guilds} verbunden", "alerts": {"label": "Warnungen", "showTitle": "Streamtitel anzeigen?", "showCategory": "Stream-<PERSON><PERSON><PERSON> anzeigen?", "showViewers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zeigen?", "showPreview": "Stream Vorschau anzeigen?", "showProfileImage": "Twitch Profil Avatar anzeigen?", "channelsSelect": "Wo werden Warnungen gepostet?", "streamOnlineLabel": "Stream-Warnmeldung", "streamOnlinePlaceholder": "<PERSON><PERSON> wird von <PERSON> gesendet, wenn der Stream gestartet wird.", "streamOfflineLabel": "Stream End-Nachricht", "streamOfflinePlaceholder": "<PERSON>se Nach<PERSON>t wird gesendet, wenn der Stream offline geht.", "updateAlert": "Twir wird die eingebettete Nachricht regelmäßig aktualisieren, um sicherzustellen, dass sie mit Ihrem Stream-Status synchronisiert wird. (Betrachter, Titel, Kategorie, etc.)", "shouldDeleteMessageOnOffline": "Warnmeldung löschen statt bearbeiten, wenn der Stream offline geht?", "additionalUsersIdsForLiveCheck": "Zusätzliche Liste von <PERSON>-<PERSON>, denen Si<PERSON> folgen können, um Stream-Benachrichtigungen zu senden."}, "connectGuild": "+ Server verbinden", "disconnectGuild": "Server trennen", "noGuilds": "Keine Server verbunden", "cannotSendMessage": "Der Bot hat keine Möglichkeit, <PERSON><PERSON><PERSON><PERSON> in diesem Kanal zu senden. Prüfen Sie die Kanalberechtigungen, um dieses Problem zu lösen."}, "faceit": {"info": "Durch den Zugriff auf diesen Dienst kann Twir Ihre Faceit-Spiele verfolgen und Befehlsvariablen wie {0}, {1} und vieles mehr einrichten."}, "nightbot": {"info": "Durch den Zugriff auf diesen Service kann Twir Befehle und <PERSON>r von <PERSON>bot importieren."}, "valorant": {"info": "Durch den Zugriff auf diesen Dienst kann Twir Ihre Valorant-Spiele verfolgen und Befehlsvariablen wie {0}, {1} und vieles mehr einrichten. "}, "sevenTv": {"description": "Wir können 7TV Channel Emotes verwalten, wenn diese Integration aktiviert ist.", "alert": "Sie können auch den erweiterten Fluss über das {0} System ausführen, um 7TV Emotes zu verwalten.", "connected": "Verbunden", "notConnected": "Nicht verbunden", "notRegistered": "Sie sind nicht bei {0} registriert", "errorSameReward": "Du kannst nicht dieselbe Belohnung für das Hinzufügen und Entfernen von Emotes verwenden!", "deleteOnlyAddedByApp": "<PERSON><PERSON> <PERSON> hinzugefügte Emotes entfernen", "connectSteps": {"step1": "Öffne dein {0} Profil", "step2": "<PERSON><PERSON><PERSON> Sie auf \"Editoren hinzufügen\" Button", "step3": "{0} als Editor hinzufügen"}, "rewardForAddEmote": "Belohnung für das Hinzufügen von Emotes", "rewardForRemoveEmote": "Belohnung für das Entfernen von Emotes", "rewardSelectorDescription": "Du solltest eine Belohnung mit Eingabe erstellen. Benutzer müssen einen 7TV Link verwenden, um die Belohnung zu aktivieren."}}, "events": {"delay": "Verzögerung (in Sek.)", "repeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineOnly": "Nur Online-Ausführung", "type": "Art", "description": "Beschreibung", "targetCommand": "Ziel-Befehl", "targetVariable": "Ziel-Variable", "targetTwitchReward": "<PERSON><PERSON> Twitch Belohnung", "targetKeyword": "Ziel-Suchwort", "operations": {"divider": "Aktionen", "name": "Aktion", "inputs": {"message": "<PERSON><PERSON> send<PERSON><PERSON> (unterstützt Variablen)", "default": "Aktionstext (unterstützt Variablen)", "vipSlots": "VIP Slots <PERSON>", "username": "Benutzername (unterstützt Variablen)", "variableValue": "Neuer Variablenwert (unterstützt Variablen)", "variableIncrementDecrement": "Wert inkrementieren/dekrementieren (unterstützt Variablen)"}, "banMessage": "Ban-Nachricht", "banTime": "<PERSON><PERSON><PERSON>", "obs": {"warningTitle": "<PERSON>e müssen OBS zuerst konfigurieren, bevor Sie fortfahren können!", "warningText": "<PERSON><PERSON> scheint, dass Sie Twir nicht mit OBS verbunden haben. <PERSON><PERSON> können dies auf der Seite \"Overlays\" tun.", "scene": "OBS Szene", "source": "OBS Quelle", "audioSource": "OBS-Audioquelle"}, "values": "<PERSON><PERSON>", "triggerAlert": "Auslöser-Alarm", "filters": {"label": "Filter", "description": "Sie können Ereignisse nach bestimmten Bedingungen filtern. Wenn du zum Beispiel \"{'{raidViewers}'} für Schlachtzugsereignisse gleich 10\" verwendest, wird die Operation nur dann ausgelöst, wenn die Anzahl der Schlachtzugmitglieder 10 beträgt.", "placeholderLeft": "Linke Seite des Zustands", "placeholderRight": "Rechte Seite des Zustands", "empty": "<PERSON><PERSON> ausgewähl<PERSON>"}}, "variables": {"userName": "Ereignisauslöser Benutzername", "userDisplayName": "Ereignisauslöser Anzeigename", "targetUserName": "<PERSON><PERSON><PERSON><PERSON>", "targetDisplayUserName": "Ereignisziel Anzeigename", "subLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (wie \"1\"/\"2\"/\"3\"/\"prime\")", "resubMonths": "Abonnentenlängenserie", "resubStreak": "Gesamtzahl der Abonnentenmonate", "resubMessage": "Benutzer-Abo <PERSON>", "rewardName": "Kanalpunkte Belohnung Name", "rewardCost": "Kanalpunkte Belohnung Preis", "rewardInput": "Kanal-Punkte-Belohnung Benutzerdefinierte Nachricht", "commandName": "Befehlname", "commandInput": "Befehlsargumente", "raidViewers": "Anzahl der Raider", "oldStreamTitle": "Alter Stream-Titel", "newStreamTitle": "Neuer Stream Titel", "oldStreamCategory": "Alte Stream Kategorie", "newStreamCategory": "Neue Stream Kategorie", "streamTitle": "Stream-Titel", "streamCategory": "Stream Kategorie", "donateAmount": "Spendenbetrag ", "donateCurrency": "Spendenwährung", "donateMessage": "Spenden-Nachricht", "keywordName": "Schlüsselwortname", "keywordResponse": "Schlüsselwort Antworten (durch Leerzeichen getrennt)", "greetingText": "Grußnachricht", "pollTitle": "Umfrage Name:", "pollOptionsNames": "Umfrageeinstellungen (durch Komma getrennt)", "pollTotalVotes": "Gesamtzahl der Abstimmungen", "pollWinnerTitle": "Umfrage Gewinner Name", "pollWinnerBitsVotes": "Anzahl der Stimmen des Gewinner Bits", "pollWinnerChannelsPointsVotes": "Anzahl der Stimmen der Gewinner Kanal-Punkte", "pollWinnerTotalVotes": "Anzahl der Channel-Punkte des Gewinners Abstimmungen", "predictionTitle": "Anzahl der Channel-Punkte des Gewinners Abstimmungen", "predictionOptionsNames": "Anzahl der Channel-Punkte des Gewinners Votes", "predictionTotalChannelPoints": "Anzahl der Channel-Punkte des Gewinners Abstimmungen", "predictionWinner": {"title": "Vorhersage Gewinner Ergebnis", "totalUsers": "Gesamte Gewinner Ergebnis Benutzer", "totalPoints": "Gesamte Channel-Punkte, die auf das Ergebnis des Gewinners gesetzt werden", "topUsers": "Top 10 Benutzer der Gewinner Ergebnisse"}, "moderatorName": "Ereignisauslöser Moderator Benutzername", "moderatorDisplayName": "Ereignisauslöser Moderator Anzeigename", "banReason": "<PERSON>", "banEndsInMinutes": "Timeout (in <PERSON>uten, oder auf \"permanent\" gesetzt, um dauerhaft zu sperren)", "message": "Nachricht"}}, "overlays": {"copyOverlayLink": "Overlay-<PERSON> kop<PERSON>en", "uncongirured": "Du solltest zuerst das Overlay konfigurieren!", "noAccess": "Sie haben keinen Zugriff auf dieses Overlay!", "copied": "Overlay-Link-URL erfolgreich kopiert! Sie müssen diese als Browser-Quelle in OBS einstellen.", "nowPlaying": {"description": "Overlay mit aktuellen Informationen zu abspielbaren Songs. Dieses Overlay funktioniert nur, wenn Sie Ihre Song-Service-Integration wie Spotify, Lastfm verbunden haben."}, "tts": {"description": "Mit diesem Overlay können Sie Chat-Nachrichten als Audio über das OBS mit einer Reihe von vorkonfigurierten Stimmen abspielen. Sie können dieses Overlay über Befehle oder Ereignisse steuern.", "eventsHint": "Tipp: <PERSON><PERSON> können das Ereignissystem verwenden, um TTS bei bestimmten Ereignissen auszulösen.", "allowUsersChooseVoice": "<PERSON><PERSON><PERSON><PERSON> Sie den Chattern, verschiedene Stimmen zu verwenden?", "doNotReadEmoji": "Emojis nicht lesen", "doNotReadTwitchEmotes": "Twitch-Emotes nicht lesen (inklusive 7TV, FFZ, BTTV", "doNotReadLinks": "<PERSON>s nicht lesen", "readChatMessages": "Alle Chat-Nachrichten lesen", "readChatMessagesNicknames": "Benutzernamen lesen", "voice": "Standardstimme", "disallowedVoices": "Nicht erlaubte Stimmliste", "volume": "Lautstärke", "pitch": "Tonhöhe", "rate": "Bewerten", "users": {"selectAll": "Alle auswählen", "undoSelection": "Auswahl rückgängig", "empty": "Niemand hat bisher seine eigenen Anpassungen erstellt..."}, "tabs": {"general": "Allgemein", "usersSettings": "Individuelle Einstellungen"}, "previewText": "Sprachvorschau mit Text, Tonhöhe und Geschwindigkeit:"}, "obs": {"description": "<PERSON><PERSON> Overlay ermöglicht es Ihnen, <PERSON><PERSON><PERSON> mit Ihrem OBS zu verbinden. Es bietet die Möglichkeit, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Audioquellen und Ereignisse zu verwalten.", "address": "Adresse des OBS WebSocket-Servers", "port": "Port des OBS WebSocket-Servers", "connected": "Verbunden!", "notConnected": "Nicht verbunden!", "checkConnection": "Verbindung prüfen"}, "alerts": {"description": "Mit diesem Overlay können Sie konfigurierte Alarme aus dem Bereich \"Alarme\" abspielen."}, "chat": {"presets": "Voreinstellungen", "selectSubsetPlaceholder": "Wählen Sie bevorzugte Untergruppen", "hideTimeout": "Zeitüberschreitung beim Ausblenden von Nachrichten", "showDelay": "Verzögerung der Nachrichtenanzeige", "showBadges": "Abzeichen anzeigen", "showAnnounceBadge": "Ankündigungsrahmen anzeigen", "paddingContainer": "Container-<PERSON><PERSON> innen", "resetToDefault": "Auf Standard zurücksetzen", "textShadow": "Textschatten", "backgroundColor": "Hintergrundfarbe", "direction": "Nachrichtenrichtung", "directions": {"left": "Links", "right": "<PERSON><PERSON><PERSON>", "top": "<PERSON><PERSON>", "bottom": "Unten"}, "directionWarning": "Um 'Links' und 'Rechts' zu verwenden, sollten Sie die Overlay-Höhe auf 50x einstellen, damit der Chat richtig angezeigt wird!", "fontFamily": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": "Schriftgröße", "fontStyle": "Schriftstil", "fontWeight": "Schriftstärke", "availabeFonts": "Verfügbare Schriftarten", "hideCommands": "Be<PERSON><PERSON>e ausblenden", "hideBots": "Andere Nachrichten von Bo<PERSON> verstecken", "style": "Nachrichten Stil", "description": "<PERSON><PERSON> Overlay erlaubt es dir, deinen T<PERSON>-Chat in deiner Streaming-Software anzuzeigen (z.B. OBS, XSplit, etc).)"}, "dudes": {"description": "Animierte Zeichen für Chatter in deinem Stream", "enable": "Aktivieren", "dudeDivider": "<PERSON><PERSON>", "dudeDefaultSprite": "Standardstimme", "dudeMaxOnScreen": "Maximal auf Bildschirm", "dudeMaxOnScreenUnlimited": "Unbegrenzt", "dudeColor": "Standard-Benutzerfarbe", "dudeGravity": "Schwerkraft", "dudeMaxLifeTime": "Maximale Lebensdauer", "dudeScale": "Skalierung", "dudeSoundsDivider": "<PERSON><PERSON><PERSON>", "dudeSoundsVolume": "Lautstärke", "ignoreDivider": "Ignorieren", "ignoreCommands": "<PERSON><PERSON><PERSON><PERSON> ignorieren", "ignoreUsers": "Ignorier<PERSON>", "ignoreUsersList": "Liste der ignorierten Benutzer", "nameBoxDivider": "Name<PERSON><PERSON>", "nameBoxFill": "Textfarbe", "nameBoxFillError": "Farbe ist erforderlich", "nameBoxFillGradientStops": "Textfarben des Farbverlauf stoppen", "nameBoxFillGradientStopsError": "Textfarben des Farbverlaufs sollten zwischen 0 und 1 liegen", "nameBoxGradientType": "Verlaufshintergrund Typ", "nameBoxFontFamily": "<PERSON><PERSON><PERSON><PERSON>", "nameBoxFontSize": "Schriftgröße", "nameBoxFontWeight": "Schriftgröße", "nameBoxFontStyle": "Schriftstil", "nameBoxFontVariant": "Schriftvariante", "nameBoxStroke": "Strichfarbe", "nameStrokeThickness": "Strichstärke", "nameBoxLineJoin": "Linienverbindung", "nameBoxDropShadow": "Schlagschatten", "nameBoxDropShadowColor": "<PERSON><PERSON><PERSON>", "nameBoxDropShadowAlpha": "Schatten Alpha", "nameBoxDropShadowBlur": "Schattenunschärfe", "nameBoxDropShadowDistance": "Schattenabstand", "nameBoxDropShadowAngle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "messageBoxIgnoreCommands": "<PERSON><PERSON><PERSON><PERSON> ignorieren", "messageBoxDivider": "Nachrichtenkiste", "messageBoxShowTime": "Zeit anzeigen", "messageBoxFill": "Textfarbe", "messageBoxBackground": "Hintergrundfarbe", "messageBoxPadding": "Füllung", "messageBoxBorderRadius": "<PERSON><PERSON>", "messageBoxFontSize": "Schriftgröße", "emoteDivider": "<PERSON><PERSON>", "growDivider": "Vergrößern", "growTime": "Zeit anzeigen", "growMaxScale": "Maximale Skalierung"}, "kappagen": {"description": "<PERSON><PERSON>lay erlaubt es dir, deinen Bildschirm mit Explosionen, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> durch den Chat zu füllen.", "testKappagen": "<PERSON><PERSON> testen", "testSpawn": "Emote-Spawn testen", "clear": "Overlay clearen", "info": "Spawn zeigt Emotes auf dem Bildschirm an, während Benutzer nur chatten und Emotes senden. Kappagen zeigt die konfigurierte Anzahl von Emotes auf einen Befehl oder konfigurierte Ereignisse.", "tabs": {"main": "Einstellungen", "events": "<PERSON><PERSON><PERSON><PERSON>", "animations": "<PERSON><PERSON>"}, "settings": {"spawn": "Ein Emote für jede Chat-Nachricht spawnen?", "size": "Größe", "sizeSmall": "Größe für kleine Emote-Variante", "time": "Emote-Lebensspanne", "maxEmotes": "<PERSON><PERSON><PERSON>", "animationsOnAppear": "Anscheinende Emote Animation", "animationsOnDisappear": "Verschwindende Emote Animation", "excludedEmotes": "Ausgeschlossene Emotes", "rave": "<PERSON><PERSON><PERSON> (Regenbogen) Emotes an", "emotes": {"bttvEnabled": "BetterTTV Emotes aktivieren?", "ffzEnabled": "FrankerFaceZ Emotes aktivieren?", "seventvEnabled": "7TV Emotes aktivieren?", "emojiStyle": "Emoji-Stil"}}}, "brb": {"description": "<PERSON><PERSON> erlaubt <PERSON>, einen Countdown auf dem Bildschirm für bestimmte Ereignisse oder während Sie AFK sind.", "settings": {"main": {"label": "Einstellungen", "startCommand": {"description": "Overlay-Start-Befehl", "example": "<b>!brb 1 Kaffee</b> – startet einen einminütigen Timer mit dem Label 'Coffee'.<br><b>!brb 1</b> – startet einen einminütigen Timer mit dem in den Abschnitten Einstellungen und Post-Timer Einstellungen angegebenen Standardtext."}, "stopCommand": {"description": "Overlay-Stop-Befehl", "example": "!brbstop"}, "text": "Overlay-Text", "background": "Overlay-Hintergrund", "backgroundOpacity": "Deckkraft des Overlay-Hintergrunds", "font": {"color": "Schriftfarbe", "family": "<PERSON><PERSON><PERSON><PERSON>", "size": "Schriftgröße"}}, "late": {"label": "Post-Timer <PERSON>ungen", "text": "Overlay Post-Timer Text", "displayBrb": "Countdown-<PERSON><PERSON> anzeigen"}}, "preview": {"start": "Overlay-Vors<PERSON>u starten", "stop": "Overlay-Vorschau stoppen"}}}, "songRequests": {"waiting": "Warte auf eine Songanfrage...", "player": {"title": "Aktueller Song", "noAccess": "Ansche<PERSON>d kannst du dort keine Songs anhören... Gehe zu deinem eigenen Dashboard „Song-Anfragen“, um auf den Player zuzugreifen."}, "table": {"title": "Warteschlange", "columns": {"title": "Titel", "author": "Autor", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "duration": "<PERSON><PERSON>"}}, "settings": {"onlineOnly": "Liedanfragen während des Streamens erlauben?", "announcePlay": "Aktuellen Song im Chat ankündigen?", "playerNoCookieMode": "Inkognito-Modus für Spieler", "playerNoCookieModeDescription": "<PERSON>n Si<PERSON> diese Option aktivieren, kannst du Songs ohne Cookies anhören und den Verlauf anzeigen. Nach der Aktivierung muss die Seite aktualisiert werden.", "neededPercentageForskip": "Prozentsatz der übersprungenen Benutzer", "channelReward": "Auslöser für Kanalpunkte-Belohnungen", "deniedChannels": "Liste gesperrter YouTube-Kan<PERSON><PERSON> (vom Anfordern von Songs)", "deniedWords": "Liste verbotener Wörter (betrifft sowohl Titel, als auch YouTube-Kanalnamen)", "deniedUsers": "<PERSON><PERSON><PERSON><PERSON>", "deniedSongs": "Gesperrte Songliste", "takeSongFromDonationMessage": "Versuche Song-Links aus Donation-Nachrichten zu erhalten?", "confirmClearQueue": "B<PERSON> du sicher, dass du die Warteschlange löschen möchtest?", "users": {"maxRequests": "Maximaler Anforderungsbetrag pro Benutzer", "minimalWatchTime": "Minimale Zuschauzeit (in Minuten)", "minimalMessages": "Minimale Nachrichtenanzahl", "minimalFollowTime": "Minimale Folgezeit (in Minuten)"}, "songs": {"maxRequests": "Maximale Song-Warteschlangen-Limit", "minLength": "Minimale Songlänge (in Minuten)", "maxLength": "Maximale Songlänge (in Minuten)", "minViews": "Minimale Anzahl an Aufrufen"}}, "tabs": {"general": "Allgemein", "users": "<PERSON><PERSON><PERSON>", "songs": "Songs", "translations": "Übersetzungen"}, "ban": {"user": "<PERSON><PERSON><PERSON>", "song": "<PERSON> bannen", "userConfirm": "Bist du dir sicher, dass du diesen Benutzer bannen möchtest?", "songConfirm": "B<PERSON> du sicher, dass du diesen Song sperren möchtest?"}}, "timers": {"table": {"columns": {"intervalInMinutes": "Intervall in Minuten", "intervalInMessages": "Chatzeilen-Intervall"}}, "modal": {"timelineDescription": "Jede Antwort wird gesendet, sobald beide Intervallbedingungen erfüllt sind.", "validations": {"nameLong": "Timer Name ist zu lang!", "nameRequired": "Timer Name ist erforderlich!", "responseRequired": "Antworttext ist erforderlich!", "responseLong": "Antwort ist zu lang!"}}, "limitExceeded": "Timer-<PERSON><PERSON>!", "newTimer": "<PERSON><PERSON><PERSON>r"}, "roles": {"modal": {"accessToUsers": "Zulässi<PERSON>utz<PERSON>", "accessByStats": "Zulä<PERSON><PERSON> Benutzer (nach Statistiken)", "requiredWatchTime": "Erforderliche Beobachtungszeit", "requiredMessages": "Benötigte Nachrichtenanzahl", "requiredChannelPoints": "Benötigte Kanalpunkte", "permissions": "Berechtigungen"}, "validations": {"nameRequired": "Name ist erforderlich!"}}, "keywords": {"title": "Schlagwörter", "info": {"text": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>hlüsselwörtern kannst du bei bestimmten passenden Wörtern eine Chat-Nachricht auslösen."}, "create": "Schlüsselwort erstellen", "triggerText": "Auslösetext", "usages": "Anzahl der Verwendungen", "isRegular": "Ist ein regulärer Ausdruck?", "regularDescription": "Regulärer Ausdruck sollte im golang-Format geschrieben werden! Siehe {0} hier.", "regularDescriptionCheatSheet": "Spickzettel", "settings": "Einstellungen", "cooldown": "Abklingzeit (in Sekunden)", "validations": {"triggerRequired": "Auslösefeld ist erforderlich!", "triggerLong": "Auslösetext ist zu lang!", "responseLong": "Antwort-Text ist zu lang!"}}, "variables": {"title": "Variablen", "type": "Art", "info": "Variablen sind ein Typ eines Kontexts, der in Bot-Antworten, Befehlen und anderen Teilen des Bots verwendet werden kann. Sie können einen konstanten Wert angeben oder ein Skrip<PERSON> setzen, um einige Werte zu erhalten, auf die der Bot normalerweise nicht einfach zugreifen kann. Skriptvariablen werden für fortgeschrittene Benutzer empfohlen.", "validations": {"nameRequired": "Variablenname ist erford<PERSON>lich!", "nameLong": "Variablenname ist zu lang"}}, "greetings": {"title": "Begrüßung", "create": "Begrüßung erstellen", "edit": "Begrüßung bearbeiten", "info": {"title": "Grüßinformationen", "text": "Grüße sind eine Art von Na<PERSON>, die <PERSON>t verwendet, um bekannte Stream-Zus<PERSON>uer willkommen zu heißen, wenn sie ihre erste Nachricht im Chat auf jedem Stream senden. Wenn Sie jeden <PERSON>er im Chat begrüßen möchten, dann können Sie sich die Veranstaltungskategorie ansehen, anstatt Grüße."}, "validations": {"userName": "Benutzername ist erford<PERSON>lich!", "textRequired": "Antworttext ist erforderlich!"}}, "alerts": {"title": "Warnungen", "info": "Warnungsinformationen", "overlayLabel": "Warnungen sind ein grafischer Teil der Befehls- oder Ereignis-Implementierung. Sie können eine Benachrichtigung mit spezifiziertem Audio, Bild und Text konfigurieren und sie dann einem bestimmten Befehl, Event, Schlüsselwort oder Gruß zuordnen. Vergessen Sie auch nicht, Overlay-URL als Browserquelle in Ihre Streaming-Software einzufügen! (z. B. OBS, XSplit, etc.)", "copyOverlayLink": "Overlay-<PERSON> kop<PERSON>en", "name": "Name", "rewards": "Bel<PERSON>nungen", "commands": "<PERSON><PERSON><PERSON><PERSON>", "validations": {"name": "Der Name darf nicht leer oder länger als 30 Zeichen sein!"}, "trigger": {"commands": "Befehlsa<PERSON>löser", "rewards": "Kanalpunkte Belohnung Trigger", "keywords": "Schlüsselwort-Auslöser", "greetings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "select": {"audio": "Konfugiere eine Audio", "image": "Konfiguriere ein Bild", "text": "Konfiguriere einen Text"}, "audioVolume": "Audiolautstärke", "createAlert": "<PERSON><PERSON><PERSON> e<PERSON>", "editAlert": "<PERSON><PERSON>", "tableEmpty": "<PERSON><PERSON> gefunden"}, "games": {"command": "<PERSON><PERSON><PERSON>", "8ball": {"description": "Ein einfaches Chatspiel, mit dem Chatter die Magie 8ball etwas fragen können und es wird auf den Chat antworten.", "answers": "Antworten"}, "russianRoulette": {"description": "Ein einfaches Chatspiel, mit dem Chatter Glück in einem klassischen russisch-lustigen Roulette-Spiel testen können.", "canBeUsedByModerator": "Timeout Moderatoren? *Moderatorenrechte werden nach dem Timeout wieder gewährt.", "timeoutSeconds": "Timeout-Zeit (in Sekunden, setzen Sie es auf 0, um ein Timeout zu deaktivieren)", "decisionSeconds": "Verzögerung (in Sek.)", "initMessage": "<PERSON><PERSON> (Game Start Nachricht)", "surviveMessage": "Benutzer hat die Nachricht überlebt", "deathMessage": "Nachricht des Benutzers verloren", "chargedBullets": "Menge aufgeladener Kugeln: {tumberSize}", "tumberSize": "Größe der Revolvertrommel"}, "duel": {"title": "<PERSON><PERSON>", "description": "Ein einfaches Chat-Spiel zwischen zwei Benutzern, bei dem Sie Ihre Ausweichfähigkeiten testen können!", "commands": {"title": "<PERSON>llbe<PERSON>hl<PERSON>", "duel": "Frage nach einem Duell", "accept": "Duell ak<PERSON><PERSON>en", "stats": "Statistiken"}, "cooldown": {"title": "Duell Abklingzeit", "user": "Individuell", "global": "Global"}, "messages": {"title": "Nachrichten", "start": {"title": "<PERSON><PERSON><PERSON>", "description": "Sendet diese <PERSON>, wenn das Duell gestartet wird. Verfügbare Variablen: {'{target}, {initiator}, {duelAcceptCommandName}, {acceptSeconds}'}"}, "result": {"title": "Ergebnisnachricht", "description": "<PERSON>et diese <PERSON>, wenn das Duell vorbei ist. Verfügbare Variablen: {'{loser}, {winner}'}"}, "bothDie": {"title": "«Niemand überlebt» Nachricht", "description": "Sendet diese Nachricht wenn Duell passiert ist und beide Duellisten tot sind. Verfügbare Variablen: {'{target}, {initiator}'}"}}, "settings": {"title": "Einstellungen", "secondsToAccept": "Sekunden zum Akzeptieren des Duells", "timeoutTime": "Zeitlimit für Verlierer", "pointsPerWin": "<PERSON><PERSON> zum <PERSON>", "pointsPerLose": "Ruf für Verlust", "bothDiePercent": "Prozentsatz zufällig sterben beide Benutzer"}}, "seppuku": {"description": "Seppuku ist eine Form des ritualistischen Selbstmords durch Entkörperung.", "message": "Nachricht", "timeoutModerators": "Aktiviere seppuku für Moderatoren", "messageModerators": "<PERSON><PERSON><PERSON><PERSON> wenn Moderator seppuku benutzt hat", "timeoutSeconds": "Timeout <PERSON>"}, "voteban": {"description": "<PERSON><PERSON><PERSON> den Prozess der Jury gegen den Schuldigen Chatter!", "banMessage": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "banMessageModerators": "Nachricht um Moderator zu sperren", "surviveMessage": "Nachricht überleben", "surviveMessageModerators": "Überlebende Nachricht für Moderatoren", "initialMessage": "<PERSON><PERSON><PERSON>", "neededVotes": "Benötigte Stimmen", "banDuration": "Dauer des Banns", "voteMode": "Modus der Abstimmung", "voteDuration": "Dauer der Abstimmung in Sekunden", "wordsNegative": "Wörter für die Abstimmung mit der Option „Nein“", "wordsPositive": "Wörter für die Abstimmung mit der Option „Ja“", "timeoutModerators": "Aktiviere Voteban gegen Moderatoren"}}, "modules": {"chatTranslations": {"title": "Chat translations", "description": "Translate chat in real time", "settings": {"title": "Chat Translation Settings", "enabled": {"label": "Enabled", "description": "Enable chat translation for your channel"}, "targetLanguage": {"label": "Target Language", "description": "Messages will be translated to this language", "placeholder": "Select a language"}, "excludedLanguages": {"label": "Excluded Languages", "description": "Messages in these languages will not be translated", "placeholder": "Select languages to exclude"}, "useItalic": {"label": "Use Italic", "description": "Display translated messages in italic"}, "excludedUsers": {"title": "Excluded Users", "description": "Messages from these users will not be translated"}, "buttons": {"update": "Update Settings", "create": "Create Settings"}}}}, "community": {"title": "Community", "users": {"title": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON>er suchen...", "table": {"user": "<PERSON><PERSON><PERSON>", "watchedTime": "Zugeschaute Zeit", "messages": "Nachrichten", "usedEmotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usedChannelPoints": "Ausgegebene Kanalpunkte", "empty": "<PERSON><PERSON>"}, "total": "Gesamte Benutzer: {total}", "reset": {"label": "Reset stats", "resetQuestion": "Sind sie sich sicher, dass sie {title} zurücksetzen wollen?"}}, "emotesStatistic": {"title": "Em<PERSON> Statistiken", "searchPlaceholder": "<PERSON><PERSON>...", "table": {"emote": "<PERSON><PERSON>", "usages": "Gesamtnutzung", "users": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON>", "chart": "Verwendungsdiagramm", "lastDay": "Tag", "lastWeek": "<PERSON><PERSON><PERSON>", "lastMonth": "<PERSON><PERSON>", "lastThreeMonth": "3 Monate", "lastYear": "<PERSON><PERSON><PERSON>"}, "total": "Gesamte Emotes: {total}", "details": {"stats": "Statistiken", "users": "<PERSON><PERSON><PERSON>", "usersTabs": {"top": "<PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "filePicker": {"innerText": "Hier {type} auswählen oder ablegen.", "usedSpace": "Verwendeter {used} / {max} MB", "emptyText": "Kein {type} hochgeladen."}, "overlaysRegistry": {"description": "In dieser Kategorie kannst du benutzerdefinierte Overlays für OBS erstellen. Derzeit werden nur HTML-Overlays unterstützt, aber andere Overlay-Varianten werden später hinzugefügt. Vielen Dank für die Geduld. <3", "noLayersCreated": {"title": "<PERSON>cht konfiguriert", "description": "Momentan sind Ebenen von Overlays nicht erstellt"}, "customWidth": "Breite", "customHeight": "<PERSON><PERSON><PERSON>", "name": "Overlay Name", "createNewLayer": "Neue Ebene erstellen", "validations": {"name": "Overlay Name darf nicht leer sein und muss länger als 30 <PERSON>eichen sein!", "layers": "<PERSON>benen dürfen nicht leer sein! Dieses Overlay darf nicht mehr als 15 Ebenen enthalten."}, "html": {"description": "Erlaubt dir benutzerdefinierte HTML-, CSS- und Bot-Variablen zu verwenden.", "updateInterval": "Aktualisierungsintervall für HTML-Variablen", "variablesAlert": {"title": "<PERSON> kannst Bot-Variablen in HTML verwenden, klicke auf die Liste unten, um verfügbare Optionen anzuzeigen.", "selectToCopy": "Wähle eine erforderliche Variable aus. Sie wird automatisch in die Zwischenablage kopiert."}, "periodicallyRefetchData": "HTML-Daten vom Server regelmäßig analysieren und aktualisieren?"}}, "chatAlerts": {"randomedMessage": "Wenn es mehrere Varianten der Chat Alerts gibt, wird zufällig ausgewählt.<br>", "randomMessageWithCount": "Wenn es mehrere Varianten mit der gleichen Zählernummer gibt, wird zufällig ausgewählt.<br>", "replacedInfo": "Verfügbare Variablen: {vars}. Sie werden durch die tatsächlichen Informationen im Alert ersetzt.<br>", "cooldown": "Nachrichten Cooldown (in Sekunden)", "ban": {"alertInfo": "Setze die Zeit auf 0, um eine Nachricht zu drucken, wenn der Benutzer permanent gesperrt ist.<br>", "countLabel": "Zeit (in Minuten)", "ignoreTimeoutFrom": "<PERSON><PERSON><PERSON><PERSON> von"}, "labels": {"followers": "Follower", "raids": "Raids", "donations": "<PERSON><PERSON><PERSON>", "subscriptions": "Abonnements", "rewards": "Bel<PERSON>nungen", "firstUserMessage": "Erste Nachricht des Benutzers", "streamOnline": "Stream Online", "streamOffline": "Stream Offline", "chatCleared": "<PERSON><PERSON> leeren", "userBanned": "Benutzer-S<PERSON>ren", "channelUnbanRequestCreate": "<PERSON><PERSON><PERSON> einen Entbannungsantrag", "channelUnbanRequestResolve": "Entbannungsantrag auflösen", "messageDelete": "Nachricht gelöscht"}}, "moderation": {"createNewRule": "<PERSON><PERSON><PERSON> eine neue Moderations-Regel", "banTime": "Timeout Zeit", "banDescription": "Auf 0 gesetzt für permanenten Bann.", "banMessage": "Timeout <PERSON>", "warningMessage": "Warnmeldung", "warningMaxCount": "Warnung Maximale <PERSON>hl", "excludedRoles": "<PERSON><PERSON><PERSON><PERSON> Rollen aus, die von diesem Filter nicht beeinflusst werden:", "types": {"links": {"name": "Links", "description": "Scannt Twitch-Chat nach Weblinks oder URLs und entfernt diese sofort, um zu verhindern, dass Spam oder unsichere Inhalte in den Chat geschickt werden."}, "deny_list": {"name": "Nicht erlaubte Wörterliste", "description": "<PERSON><PERSON>t beim Erhalt eines sauberen Chats, indem man Wörter oder Phrasen aus einer vordefinierten Liste herausfiltert und verhindert, dass sie angezeigt werden.", "empty": "Liste der nicht erlaubten Wörter ist leer! Du kannst dort welche hinzufügen.", "regexp": "Wenn du ein fortgeschrittener Benutzer bist, unterstützt die Liste nicht erlaubter Wörter auch reguläre Ausdrücke. Reguläre Ausdrücke sollten im golang-Format geschrieben werden! Siehe {0} hier.", "regexpCheatSheet": "Spickzettel"}, "symbols": {"name": "Symbole", "description": "Identifiziert und beschränkt den übermäßigen Gebrauch von Symbolen, verhindert Unklarheiten, Chaos und hält die Lesbarkeit im Chat aufrecht.", "triggerLength": "Minimale Nachrichtenlänge", "maxPercentage": "Maximaler Prozentsatz der Symbole"}, "long_message": {"name": "Nachrichtenlänge", "description": "Identifiziert und beschränkt lange Nachrichten, indem die Länge der Nachrichten im Chat begrenzt wird.", "triggerLength": "Maximale Nachrichtenlänge"}, "caps": {"name": "Großbuchstaben", "description": "Identifiziert und beschränkt übermäßige Verwendung von Großbuchstaben und fördert ein ausgewogeneres und respektvolleres Gespräch im Chat.", "maxPercentage": "Maximaler Prozentsatz der Großbuchstaben", "triggerLength": "Minimale Nachrichtenlänge"}, "emotes": {"name": "Emotes", "description": "Erkennt die Häufigkeit und die Menge der Emoticons, die in Nachrichten verwendet werden, um Überlastung und Spam zu vermeiden.", "triggerLength": "Maximales Emote Nutzungslimit"}, "language": {"name": "<PERSON><PERSON><PERSON>", "description": "Scannt und filtert unangemessene Sprachen heraus.", "allowedLanguages": "Liste der erlaubten Sprachen", "disallowedLanguages": "Liste der nicht erlaubten Sprachen"}}}, "feedback": {"button": "Feed<PERSON> senden", "notification": "<PERSON><PERSON> wurde gesendet", "rateLimited": "Sie senden Nachrichten zu schnell, versuchen sie es in {time} erneut", "messageLabel": "Nachricht", "validation": {"emptyMessage": "<PERSON> Nachricht darf nicht leer sein"}}, "userSettings": {"title": "Benutzereinstellungen", "account": {"title": "Account", "showMeOnLanding": "Lasse dich auf der Start-Seite zeigen?", "regenerateApiKey": {"button": "Privaten API Schlüssel neu generieren", "info": "<PERSON>ser Schlüssel wird für alle Overlay-Links verwendet. Wenn du es neu generierst, musst du alle deiner Overlays in deiner Streaming-Software überprüfen und URLs durch die neuen Links ersetzen."}}, "public": {"title": "Öffentliche Seite", "description": "Beschreibung des Kanals", "socialLinks": "Soziale Netzwerke", "errorCreateLink": "\"{title}\" enthält keinen gültigen Link {href}", "errorInvalidLength": "Ungültige Länge", "errorInvalidLink": "Ungültiger Link", "errorEmpty": "Kann nicht leer sein", "errorTooLong": "<PERSON><PERSON> lang", "linkTitle": "Titel", "linkLabel": "Verknüpfung"}}, "adminPanel": {"title": "<PERSON><PERSON>", "notifications": {"title": "Benachrichtigungen", "formTitle": "Benachrichtigung senden oder bearbeiten", "emptyNotifications": "<PERSON><PERSON>", "userLabel": "<PERSON><PERSON><PERSON>", "userPlaceholder": "Benutzer wählen", "messageLabel": "Nachricht", "messagePreview": "Vorschau", "createdAt": "Erstellt am", "globals": "Global", "users": "<PERSON><PERSON><PERSON>", "confirmResetForm": "B<PERSON> du sicher, dass du deine Änderungen zurücksetzen möchtest?"}, "manageUsers": {"title": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "userId": "Benutzer-ID", "followers": "Followers", "noUsers": "<PERSON><PERSON>", "filters": "Filter", "clearFilters": "<PERSON><PERSON>", "countSelected": "{count} ausgewählt", "isAdmin": "Admin", "isBanned": "Banned", "isBotEnabled": "Bot aktiviert", "statusGroup": "Status", "badgesGroup": "Abzeichen", "giveBan": "<PERSON><PERSON><PERSON>", "unBan": "Benutzer entsperren", "giveMod": "<PERSON><PERSON> geben", "unMod": "<PERSON><PERSON>", "dropSession": "Sitzung abbrechen"}, "manageBadges": {"title": "Abzeichen", "formTitle": "Abzeichen erstellen oder bearbeiten", "name": "Name", "users": "<PERSON><PERSON><PERSON>", "slot": "Slot", "image": "Bild", "preview": "Vorschau", "usesCount": "<PERSON><PERSON><PERSON><PERSON> {count}", "badgeSlot": "Slot {slot}"}, "adminActions": {"title": "Aktionen", "eventsub": {"title": "EventSub abonnieren", "condition": "Bedingung", "type": "<PERSON><PERSON>", "typeError": "<PERSON><PERSON><PERSON> einen <PERSON> an", "version": "Version", "user": "<PERSON><PERSON><PERSON>", "channel": "<PERSON><PERSON>", "channelWithBotId": "Ka<PERSON> mit Bot-ID", "channelWithModeratorId": "Kanal mit Moderator-ID"}, "dangerZone": {"title": "Gefahrenzone", "revoke": "Widerrufen", "revokeSessions": "Sitzungen widerrufen", "revokeAllSessionsDescription": "Alle Benutzersitzungen werden widerrufen", "revokeAllSessionsConfirm": "B<PERSON> du sicher, dass du alle Sitzungen widerrufen möchtest?"}}, "auditLogs": {"title": "Audit-Logs"}}}