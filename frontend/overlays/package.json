{"name": "@twir/overlays", "private": true, "type": "module", "scripts": {"dev": "bunx --bun vite", "build": "bun run codegen && vue-tsc && vite build", "preview": "bunx --bun vite preview", "codegen": "bunx --bun graphql-codegen"}, "dependencies": {"@plugin-web-update-notification/vite": "1.7.1", "@protobuf-ts/twirp-transport": "2.9.4", "@twir/api": "workspace:*", "@twir/fontsource": "workspace:*", "@twir/frontend-chat": "workspace:*", "@twir/frontend-faceit-stats": "workspace:*", "@twir/frontend-now-playing": "workspace:*", "@twir/grpc": "workspace:*", "@twirapp/dudes-vue": "0.1.0", "@twirapp/kappagen": "1.0.1", "@urql/vue": "1.1.3", "@vueuse/core": "catalog:", "@zero-dependency/utils": "1.7.7", "emoji-regex": "10.3.0", "graphql": "catalog:", "graphql-ws": "catalog:", "nested-css-to-flat": "1.0.5", "obs-websocket-js": "5.0.5", "tmi.js": "1.8.5", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.2.5", "@graphql-typed-document-node/core": "3.2.0", "@twir/types": "workspace:*", "@types/tmi.js": "1.8.6", "@vitejs/plugin-vue": "5.0.4", "typescript": "catalog:", "vite": "catalog:", "vite-plugin-watch": "0.3.1", "vue-tsc": "catalog:"}}