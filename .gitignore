node_modules
.env
traefik/.env
dist
tsconfig.tsbuildinfo
.turbo
.pnpm-debug.log
libs/grpc/go/bots
libs/grpc/go/streamstatus
libs/grpc/src/*.ts
!libs/grpc/src/index.ts

docker-compose*
!docker-compose.example.yml
!docker-compose.dev.yml
!docker-compose.build.yml
!docker-compose.stack.yml
!docker-compose.dockprom.yml
.devbox
devbox/go
.pnpm-store

!*/**/.gitkeep

main
main.~

.idea
!.idea/codeStyles
!.idea/codeStyles/*.xml
!.idea/dataSources.xml

!traefik/*

vite.config.ts.timestamp-*.mjs

.DS_STORE
.bin
.out

*.backup
configs/otel/otel-collector.yaml
