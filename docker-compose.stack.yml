version: '3.9'

services:
  adminer:
    image: adminer
    networks:
      - twir
      - traefik-public
    deploy:
      placement:
        constraints:
          - node.labels.databases != true
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-adminer.rule=Host(`adminer.twir.app`)
        - traefik.http.services.twir-adminer.loadbalancer.server.port=8080
        - traefik.docker.network=traefik-public

  nats:
    image: nats:2.10.11-scratch
    restart: always
    command: -js -m 8222
    networks:
      - twir
      - traefik-public
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-nats.rule=Host(`nats.twir.app`)
        - traefik.http.services.twir-nats.loadbalancer.server.port=8222
        - traefik.docker.network=traefik-public

  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.116.1
    volumes:
      - ./configs/otel/otel-collector.yaml:/etc/otelcol-contrib/config.yaml
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 3s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.role == manager

#  postgres-old:
#    image: 'bitnami/postgresql:17'
#    environment:
#      POSTGRES_USER_FILE: /run/secrets/twir_postgres_user
#      POSTGRES_PASSWORD_FILE: /run/secrets/twir_postgres_password
#      POSTGRES_DB_FILE: /run/secrets/twir_postgres_db
#      POSTGRESQL_POSTGRES_PASSWORD_FILE: /run/secrets/twir_postgres_password
#      POSTGRESQL_MAX_CONNECTIONS: 1000
#    secrets:
#      - twir_postgres_user
#      - twir_postgres_password
#      - twir_postgres_db
#    volumes:
#      - twir-postgres-data:/bitnami/postgresql
#    networks:
#      - twir
#    deploy:
#      restart_policy:
#        condition: any
#        delay: 30s
#        max_attempts: 30
#      endpoint_mode: dnsrr
#      placement:
#        constraints:
#          - node.labels.databases-1 == tru

  postgres:
    image: twirapp/postgres:latest
    command: postgres -c max_connections=1000 -c shared_buffers=2GB -c effective_cache_size=6GB -c maintenance_work_mem=512MB -c checkpoint_completion_target=0.9 -c wal_buffers=16MB -c default_statistics_target=100 -c random_page_cost=1.1 -c effective_io_concurrency=200 -c work_mem=32MB -c huge_pages=off -c min_wal_size=1GB -c max_wal_size=4GB -c shared_preload_libraries='pg_stat_statements,pgx_ulid'
    environment:
      POSTGRES_USER_FILE: /run/secrets/twir_postgres_user
      POSTGRES_PASSWORD_FILE: /run/secrets/twir_postgres_password
      POSTGRES_DB_FILE: /run/secrets/twir_postgres_db
    secrets:
      - twir_postgres_user
      - twir_postgres_password
      - twir_postgres_db
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases-1 == true

  postgres-backup:
    image: registry.twir.app/twirapp/postgres-backup:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases == true

  pgbouncer:
    image: bitnami/pgbouncer:1.23.1
    secrets:
      - twir_postgres_user
      - twir_postgres_password
      - twir_postgres_db
    environment:
      - POSTGRESQL_HOST=postgres
      - POSTGRESQL_USERNAME_FILE=/run/secrets/twir_postgres_user
      - POSTGRESQL_PASSWORD_FILE=/run/secrets/twir_postgres_password
      - POSTGRESQL_DATABASE_FILE=/run/secrets/twir_postgres_db

      - PGBOUNCER_AUTH_USER=twir
      - PGBOUNCER_DATABASE=twir
      - PGBOUNCER_DEFAULT_POOL_SIZE=19
      - PGBOUNCER_MIN_POOL_SIZE=10
      - PGBOUNCER_MAX_CLIENT_CONN=4000
      - PGBOUNCER_POOL_MODE=transaction
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      mode: replicated
      replicas: 5
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr

  clickhouse:
    image: clickhouse/clickhouse-server:25.5-alpine
    networks:
      - twir
      - cloudflared
    volumes:
      - clickhouse-data:/var/lib/clickhouse
    configs:
      - source: clickhouse_config
        target: /etc/clickhouse-server/config.d/override.xml
    environment:
      - CLICKHOUSE_USER=twir
      - CLICKHOUSE_PASSWORD=twir
      - CLICKHOUSE_DB=twir
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '2.0'
          memory: 3G
      restart_policy:
        condition: on-failure
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases-1 == true
    ulimits:
      nofile: 262144
    cap_add:
      - SYS_NICE
      - NET_ADMIN
      - IPC_LOCK

  temporal-postgres:
    image: 'bitnami/postgresql:17'
    environment:
      POSTGRES_USER: temporal
      POSTGRES_PASSWORD: temporal
      POSTGRES_DB: temporal
      POSTGRESQL_POSTGRES_PASSWORD: temporal
    volumes:
      - temporal-postgres-data:/bitnami/postgresql
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases-1 == true

  temporal:
    depends_on:
      - postgres
    image: twirapp/temporal:latest
    networks:
      - twir
    secrets:
      - twir_postgres_user
      - twir_postgres_password
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      labels:
        kompose.volume.type: configMap
      placement:
        constraints:
          - node.labels.databases != true

  temporal-ui:
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000
    image: temporalio/ui:2.21.0
    networks:
      - twir
      - traefik-public
    deploy:
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-temporal.rule=Host(`temporal.twir.app`)

        - traefik.http.services.twir-temporal.loadbalancer.server.port=8080
        - traefik.docker.network=traefik-public
      placement:
        constraints:
          - node.labels.databases != true

  migrations:
    image: registry.twir.app/twirapp/migrations:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      restart_policy:
        condition: on-failure
        delay: 30s
        max_attempts: 30
    healthcheck:
      test: exit 0

  redis:
    image: redis:8.0.2
    command: redis-server --save 60 1 --loglevel warning --io-threads 4
    volumes:
      - redis-data:/data
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 3s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases == true

  api:
    image: registry.twir.app/twirapp/api:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
    deploy:
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-api.rule=Host(`twir.app`) && PathPrefix(`/api-old`)
        - traefik.http.routers.twir-api.middlewares=api-stripprefix
        - traefik.http.middlewares.api-stripprefix.stripprefix.prefixes=/api-old
        - traefik.http.services.twir-api.loadbalancer.server.port=3002
        - traefik.docker.network=traefik-public
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.databases != true

  api-gql:
    image: registry.twir.app/twirapp/api-gql:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
    deploy:
      labels:
        - traefik.enable=true

        # Router for /api (twir-api-gql)
        - "traefik.http.routers.twir-api-gql.rule=Host(`twir.app`) && PathPrefix(`/api`)"
        - "traefik.http.routers.twir-api-gql.service=twir-api-gql"
        - "traefik.http.routers.twir-api-gql.middlewares=api-gql-stripprefix"
        - "traefik.http.middlewares.api-gql-stripprefix.stripprefix.prefixes=/api"

        # Router for /s (twir-api-shortener-redirect)
        - "traefik.http.routers.twir-api-shortener.rule=Host(`twir.app`) && PathPrefix(`/s`)"
        - "traefik.http.routers.twir-api-shortener.service=twir-api-gql"
        - "traefik.http.middlewares.api-shortener-rewrite.replacepathregex.regex=^/s/(.*)"
        - "traefik.http.middlewares.api-shortener-rewrite.replacepathregex.replacement=/v1/short-links/$$1"
        - "traefik.http.routers.twir-api-shortener.middlewares=api-shortener-rewrite"

        # Service definition (shared for both routers)
        - "traefik.http.services.twir-api-gql.loadbalancer.server.port=3009"

        - traefik.docker.network=traefik-public
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.databases != true

  bots:
    image: registry.twir.app/twirapp/bots:latest
    secrets:
      - twir_doppler_token
    networks:
      - traefik-public
      - twir
    deploy:
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-bots.rule=Host(`services-bots.twir.app`)

        - traefik.http.services.twir-bots.loadbalancer.server.port=3000
        - traefik.docker.network=traefik-public
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      replicas: 4
      mode: replicated
      update_config:
        parallelism: 2
      placement:
        constraints:
          - node.labels.databases != true

  parser:
    image: registry.twir.app/twirapp/parser:latest
    secrets:
      - twir_doppler_token
    deploy:
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      mode: replicated
      replicas: 6
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true
    networks:
      - twir

  timers:
    image: registry.twir.app/twirapp/timers:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      mode: replicated
      replicas: 6
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true

  scheduler:
    image: registry.twir.app/twirapp/scheduler:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true

  eventsub:
    image: registry.twir.app/twirapp/eventsub:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
      - cloudflared
    deploy:
      mode: replicated
      replicas: 3
      update_config:
        parallelism: 1
      endpoint_mode: dnsrr
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-eventsub.rule=Host(`eventsub.twir.app`)
        - traefik.http.services.twir-eventsub.loadbalancer.server.port=3003
        - traefik.docker.network=traefik-public
      placement:
        constraints:
          - node.labels.databases != true

  integrations:
    image: registry.twir.app/twirapp/integrations:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr

  web:
    image: registry.twir.app/twirapp/web:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
    deploy:
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      mode: replicated
      replicas: 4
      endpoint_mode: dnsrr
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-web.rule=Host(`twir.app`)

        - traefik.http.services.twir-web.loadbalancer.server.port=3000
        - traefik.docker.network=traefik-public
      placement:
        constraints:
          - node.labels.databases != true

  dashboard:
    image: registry.twir.app/twirapp/dashboard:latest
    command: --gzip --brotli --threshold 500 --ignore-cache-control-paths "/sw.js,/index.html,/manifest.webmanifest,/pluginWebUpdateNotice/web_version_by_plugin.json"
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
      - cloudflared
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-dashboard.rule=Host(`twir.app`) && PathPrefix(`/dashboard`)
        - traefik.http.routers.twir-dashboard.middlewares=dashboard-stripprefix
        - traefik.http.middlewares.dashboard-stripprefix.stripprefix.prefixes=/dashboard
        - traefik.http.services.twir-dashboard.loadbalancer.server.port=8080
        - traefik.docker.network=traefik-public
      placement:
        constraints:
          - node.labels.databases != true

  overlays:
    image: registry.twir.app/twirapp/overlays:latest
    command: --gzip --brotli --threshold 500
    networks:
      - twir
      - traefik-public
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-overlays.rule=Host(`twir.app`) && PathPrefix(`/overlays`)
        - traefik.http.routers.twir-overlays.middlewares=overlays-stripprefix
        - traefik.http.middlewares.overlays-stripprefix.stripprefix.prefixes=/overlays
        - traefik.http.services.twir-overlays.loadbalancer.server.port=8080
        - traefik.docker.network=traefik-public
      placement:
        constraints:
          - node.labels.databases != true

  websockets:
    image: registry.twir.app/twirapp/websockets:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      labels:
        - traefik.enable=true
        - traefik.http.routers.twir-websockets.rule=Host(`twir.app`) && PathPrefix(`/socket`)
        - traefik.http.routers.twir-websockets.middlewares=sockets-stripprefix
        - traefik.http.middlewares.sockets-stripprefix.stripprefix.prefixes=/socket
        - traefik.http.services.twir-websockets.loadbalancer.server.port=3004
        - traefik.docker.network=traefik-public
      placement:
        constraints:
          - node.labels.databases != true

  tokens:
    image: registry.twir.app/twirapp/tokens:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      mode: replicated
      replicas: 4
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true

  emotes-cacher:
    image: registry.twir.app/twirapp/emotes-cacher:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      mode: replicated
      replicas: 4
      placement:
        constraints:
          - node.labels.databases != true

  events:
    image: registry.twir.app/twirapp/events:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      mode: replicated
      replicas: 6
      placement:
        constraints:
          - node.labels.databases != true

  ytsr:
    image: registry.twir.app/twirapp/ytsr:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      mode: replicated
      replicas: 6
      placement:
        constraints:
          - node.labels.databases != true

  tts:
    image: aculeasis/rhvoice-rest:latest
    networks:
      - twir
    deploy:
      update_config:
        parallelism: 2
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      mode: replicated
      replicas: 4
      placement:
        constraints:
          - node.labels.databases != true

  discord:
    image: registry.twir.app/twirapp/discord:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true

  language-processor:
    image: registry.twir.app/twirapp/language-processor:latest
    secrets:
      - twir_doppler_token
    environment:
      - APP_ENV=production
    deploy:
      update_config:
        parallelism: 2
      mode: replicated
      replicas: 4
      endpoint_mode: dnsrr
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      placement:
        constraints:
          - node.labels.databases != true
          - node.role != manager
    networks:
      - cloudflared
      - twir

  chat-translator:
    image: registry.twir.app/twirapp/chat-translator:latest
    secrets:
      - twir_doppler_token
    environment:
      - APP_ENV=production
    deploy:
      update_config:
        parallelism: 2
      mode: replicated
      replicas: 4
      endpoint_mode: dnsrr
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      placement:
        constraints:
          - node.labels.databases != true
          - node.role != manager
    networks:
      - twir

  toxicity-detector:
    image: registry.twir.app/twirapp/toxicity-detector:latest
    environment:
      TOXICITY_THRESHOLD: -4
    deploy:
      update_config:
        parallelism: 2
      mode: replicated
      replicas: 4
      endpoint_mode: dnsrr
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      placement:
        constraints:
          - node.labels.databases != true
          - node.role != manager
    networks:
      - twir

  # This is https://github.com/twirapp/music-recognizer repository, which is doing "Shazam for twitch stream".
  music-recognizer:
    image: registry.twir.app/twirapp/music-recognizer:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
      - traefik-public
    deploy:
      labels: # for testing purposes, not needed in production because requests done via internal docker network
        - traefik.enable=true
        - traefik.http.routers.music-recognizer.rule=Host(`music-recognizer.twir.app`)
        - traefik.http.services.music-recognizer.loadbalancer.server.port=3000
        - traefik.docker.network=traefik-public
      update_config:
        parallelism: 1
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      mode: replicated
      replicas: 4
      endpoint_mode: dnsrr
      placement:
        constraints:
          - node.labels.databases != true

  giveaways:
    image: registry.twir.app/twirapp/giveaways:latest
    secrets:
      - twir_doppler_token
    networks:
      - twir
    deploy:
      restart_policy:
        condition: any
        delay: 30s
        max_attempts: 30
      endpoint_mode: dnsrr
      replicas: 4
      mode: replicated
      update_config:
        parallelism: 2
      placement:
        constraints:
          - node.labels.databases != true

configs:
  clickhouse_config:
    file: ./configs/clickhouse-config.xml

volumes:
  postgres-data:
  redis-data:
  minio-data:
  temporal-postgres-data:
  clickhouse-data:

networks:
  twir:
    name: twir
    external: true
  traefik-public:
    external: true
  cloudflared:
    external: true

secrets:
  twir_doppler_token:
    external: true
  twir_postgres_user:
    external: true
  twir_postgres_db:
    external: true
  twir_postgres_password:
    external: true
