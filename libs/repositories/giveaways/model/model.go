package model

import (
	"time"

	"github.com/oklog/ulid/v2"
)

type ChannelGiveaway struct {
	ID              ulid.ULID  `db:"id"`
	ChannelID       string     `db:"channel_id"`
	CreatedAt       time.Time  `db:"created_at"`
	Keyword         string     `db:"keyword"`
	UpdatedAt       time.Time  `db:"updated_at"`
	StartedAt       *time.Time `db:"started_at"`
	StoppedAt       *time.Time `db:"stopped_at"`
	CreatedByUserID string     `db:"created_by_user_id"`
}

var ChannelGiveawayNil = ChannelGiveaway{}
