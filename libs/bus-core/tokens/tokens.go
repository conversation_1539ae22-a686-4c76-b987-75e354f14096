package tokens

const (
	RequestAppTokenSubject    = "tokens.request_app_token"
	RequestUserTokenSubject   = "tokens.request_user_token"
	RequestBotTokenSubject    = "tokens.request_bot_token"
	UpdateBotTokenSubject     = "tokens.update_bot_token"
	UpdateUserTokenSubject    = "tokens.update_user_token"
)

type Token struct {
	AccessToken string   `json:"accessToken"`
	Scopes      []string `json:"scopes"`
	ExpiresIn   int32    `json:"expiresIn"`
}

type GetUserTokenRequest struct {
	UserID string `json:"userId"`
}

type GetBotTokenRequest struct {
	BotID string `json:"botId"`
}

type UpdateTokenRequest struct {
	AccessToken  string   `json:"accessToken"`
	RefreshToken string   `json:"refreshToken"`
	ExpiresIn    int64    `json:"expiresIn"`
	Scopes       []string `json:"scopes"`
}
