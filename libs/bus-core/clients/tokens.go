package clients

import (
	"context"

	buscore "github.com/twirapp/twir/libs/bus-core"
	"github.com/twirapp/twir/libs/bus-core/tokens"
)

type TokensClient struct {
	bus *buscore.Bus
}

func NewTokensClient(bus *buscore.Bus) *TokensClient {
	return &TokensClient{
		bus: bus,
	}
}

func (c *TokensClient) RequestAppToken(ctx context.Context) (*tokens.Token, error) {
	resp, err := c.bus.Tokens.RequestAppToken.Request(ctx, struct{}{})
	if err != nil {
		return nil, err
	}
	return &resp.Data, nil
}

func (c *TokensClient) RequestUserToken(ctx context.Context, req *tokens.GetUserTokenRequest) (*tokens.Token, error) {
	resp, err := c.bus.Tokens.RequestUserToken.Request(ctx, *req)
	if err != nil {
		return nil, err
	}
	return &resp.Data, nil
}

func (c *TokensClient) RequestBotToken(ctx context.Context, req *tokens.GetBotTokenRequest) (*tokens.Token, error) {
	resp, err := c.bus.Tokens.RequestBotToken.Request(ctx, *req)
	if err != nil {
		return nil, err
	}
	return &resp.Data, nil
}

func (c *TokensClient) UpdateBotToken(ctx context.Context, req *tokens.UpdateTokenRequest) error {
	_, err := c.bus.Tokens.UpdateBotToken.Request(ctx, *req)
	return err
}

func (c *TokensClient) UpdateUserToken(ctx context.Context, req *tokens.UpdateTokenRequest) error {
	_, err := c.bus.Tokens.UpdateUserToken.Request(ctx, *req)
	return err
}
