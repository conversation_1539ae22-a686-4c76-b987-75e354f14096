package buscore

const (
	AUDIT_LOGS_SUBJECT                                 = "audit_logs.logs"
	PARSER_COMMANDS_SUBJECT                            = "paser.commands_queue"
	PARSER_TEXT_VARIABLES_SUBJECT                      = "parser.parse_text_variables"
	PARSER_PROCESS_MESSAGE_AS_COMMAND_SUBJECT          = "parser.process_message_as_command"
	STREAM_ONLINE_SUBJECT                              = "stream.online"
	STREAM_OFFLINE_SUBJECT                             = "stream.offline"
	CHAT_MESSAGES_SUBJECT                              = "chat.messages"
	CHAT_MESSAGES_STORE_GET_BY_TEXT_FOR_DELETE_SUBJECT = "chat_messages_store.get_by_text_for_delete"
	CHAT_MESSAGES_STRORE_REMOVE_MESSAGES_SUBJECT       = "chat_messages_store.remove_messages"
	TOKENS_REQUEST_APP_TOKEN_SUBJECT                   = "tokens.request_app_token"
	TOKENS_REQUEST_USER_TOKEN_SUBJECT                  = "tokens.request_user_token"
	TOKENS_REQUEST_BOT_TOKEN_SUBJECT                   = "tokens.request_bot_token"
	TOKENS_UPDATE_BOT_TOKEN_SUBJECT                    = "tokens.update_bot_token"
	TOKENS_UPDATE_USER_TOKEN_SUBJECT                   = "tokens.update_user_token"
)
