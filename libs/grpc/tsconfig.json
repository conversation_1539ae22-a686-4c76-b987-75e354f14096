{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "declaration": true, "declarationMap": true, "preserveSymlinks": true, "composite": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "include": ["./clients/*.ts", "./constants/*.ts"], "exclude": ["node_modules"]}