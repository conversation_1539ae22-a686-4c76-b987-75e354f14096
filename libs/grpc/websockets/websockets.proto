syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/grpc/websockets";
package websockets;
import "google/protobuf/empty.proto";

service Websocket {
	rpc YoutubeAddSongToQueue(YoutubeAddSongToQueueRequest) returns (google.protobuf.Empty) {}
	rpc YoutubeRemoveSongToQueue(YoutubeRemoveSongFromQueueRequest) returns (google.protobuf.Empty) {}

	rpc ObsCheckIsUserConnected(ObsCheckUserConnectedRequest) returns (ObsCheckUserConnectedResponse) {}
	rpc ObsSetScene(ObsSetSceneMessage) returns (google.protobuf.Empty) {}
	rpc ObsToggleSource(ObsToggleSourceMessage) returns (google.protobuf.Empty) {}
	rpc ObsToggleAudio(ObsToggleAudioMessage) returns (google.protobuf.Empty) {}
	rpc ObsAudioSetVolume(ObsAudioSetVolumeMessage) returns (google.protobuf.Empty) {}
	rpc ObsAudioIncreaseVolume(ObsAudioIncreaseVolumeMessage) returns (google.protobuf.Empty) {}
	rpc ObsAudioDecreaseVolume(ObsAudioDecreaseVolumeMessage) returns (google.protobuf.Empty) {}
	rpc ObsAudioEnable(ObsAudioDisableOrEnableMessage) returns (google.protobuf.Empty) {}
	rpc ObsAudioDisable(ObsAudioDisableOrEnableMessage) returns (google.protobuf.Empty) {}
	rpc ObsStopStream(ObsStopOrStartStream) returns (google.protobuf.Empty) {}
	rpc ObsStartStream(ObsStopOrStartStream) returns (google.protobuf.Empty) {}

	rpc TextToSpeechSay(TTSMessage) returns (google.protobuf.Empty) {}
	rpc TextToSpeechSkip(TTSSkipMessage) returns (google.protobuf.Empty) {}

	rpc TriggerAlert(TriggerAlertRequest) returns (google.protobuf.Empty) {}

	rpc TriggerKappagen(TriggerKappagenRequest) returns (google.protobuf.Empty) {}
	rpc TriggerKappagenByEvent(TriggerKappagenByEventRequest) returns (google.protobuf.Empty) {}

	rpc TriggerShowBrb(TriggerShowBrbRequest) returns (google.protobuf.Empty) {}
	rpc TriggerHideBrb(TriggerHideBrbRequest) returns (google.protobuf.Empty) {}

	rpc RefreshOverlaySettings(RefreshOverlaysRequest) returns (google.protobuf.Empty) {}

	rpc DudesJump(DudesJumpRequest) returns (google.protobuf.Empty) {}
	rpc DudesUserPunished(DudesUserPunishedRequest) returns (google.protobuf.Empty) {}
}

enum RefreshOverlaySettingsName {
	CUSTOM = 0;
	KAPPAGEN = 1;
	BRB = 2;
	DUDES = 3;
	CHAT = 4;
	NOW_PLAYING = 5;
}

message YoutubeAddSongToQueueRequest {
	string channelId = 1;
	string entityId = 2;
}

message YoutubeRemoveSongFromQueueRequest {
	string channelId = 1;
	string entityId = 2;
}

message ObsSetSceneMessage {
	string channelId = 1;
	string sceneName = 2;
}

message ObsToggleSourceMessage {
	string channelId = 1;
	string sourceName = 2;
}

message ObsToggleAudioMessage {
	string channelId = 1;
	string audioSourceName = 2;
}

message ObsAudioSetVolumeMessage {
	string channelId = 1;
	string audioSourceName = 2;
	uint32 volume = 3;
}

message ObsAudioIncreaseVolumeMessage {
	string channelId = 1;
	string audioSourceName = 2;
	uint32 step = 3;
}

message ObsAudioDecreaseVolumeMessage {
	string channelId = 1;
	string audioSourceName = 2;
	uint32 step = 3;
}

message ObsAudioDisableOrEnableMessage {
	string channelId = 1;
	string audioSourceName = 2;
}

message ObsStopOrStartStream {
	string channelId = 1;
}

message TTSMessage {
	string channelId = 1;
	string text = 2;
	string voice = 3;
	string rate = 4;
	string pitch = 5;
	string volume = 6;
}

message TTSSkipMessage {
	string channelId = 1;
}

message ObsCheckUserConnectedRequest {
	string user_id = 1;
}

message ObsCheckUserConnectedResponse {
	bool state = 1;
}

message TriggerAlertRequest {
	string channel_id = 1;
	string alert_id = 2;
}

message RefreshOverlaysRequest {
	string channel_id = 1;
	RefreshOverlaySettingsName overlay_name = 2;
	optional string overlay_id = 3;
}

message TriggerKappagenRequest {
	message Emote {
		string id = 1;
		repeated string positions = 2;
	}

	string channel_id = 1;
	string text = 2;
	repeated Emote emotes = 3;
}

message TriggerKappagenByEventRequest {
	string channel_id = 1;
	int32 event = 2;
}

message TriggerShowBrbRequest {
	string channel_id = 1;
	int32 minutes = 2;
	optional string text = 3;
}

message TriggerHideBrbRequest {
	string channel_id = 1;
}

message DudesJumpRequest {
	string channel_id = 1;
	string user_id = 2;
	string user_display_name = 3;
	string user_name = 4;
	string user_color = 5;
}

message DudesUserPunishedRequest {
	string channel_id = 1;
	string user_id = 2;
	string user_display_name = 3;
	string user_name = 4;
}
