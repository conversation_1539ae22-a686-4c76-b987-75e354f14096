syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/api/messages/twitch";
package messages.twitch;

message TwitchGetUsersRequest {
	repeated string ids = 1;
	repeated string names = 2;
}

message TwitchUser {
	string id = 1;
	string login = 2;
	string display_name = 3;
	string type = 4;
	string broadcaster_type = 5;
	string description = 6;
	string profile_image_url = 7;
	string offline_image_url = 8;
	string created_at = 9;
}

message TwitchGetUsersResponse {
	repeated TwitchUser users = 1;
}

message TwitchSearchChannelsRequest {
	string query = 1;
	bool twir_only = 2;
}

message Channel {
	string id = 1;
	string login = 2;
	string display_name = 3;
	string profile_image_url = 4;
	string title = 5;
	string game_name = 6;
	string game_id = 7;
	bool is_live = 8;
}

message TwitchSearchChannelsResponse {
	repeated Channel channels = 1;
}
