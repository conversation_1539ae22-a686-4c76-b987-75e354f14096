syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/api/messages/overlays";
package messages.overlays;

enum OverlayLayerType {
	HTML = 0;
}

message OverlayLayerSettings {
	string html_overlay_html = 1;
	string html_overlay_css = 2;
	string html_overlay_js = 3;
	int32 html_overlay_html_data_poll_seconds_interval = 4;
}

message OverlayLayer {
	string id = 1;
	OverlayLayerType type = 2;
	OverlayLayerSettings settings = 3;
	string overlay_id = 4;
	int32 pos_x = 5;
	int32 pos_y = 6;
	int32 width = 7;
	int32 height = 8;
	string createdAt = 9;
	string updatedAt = 10;
	bool periodically_refetch_data = 11;
}

message Overlay {
	string id = 1;
	string channel_id = 2;
	string name = 3;
	string createdAt = 4;
	string updatedAt = 5;
	repeated OverlayLayer layers = 6;
	int32 width = 7;
	int32 height = 8;
}

message GetByIdRequest {
	string id = 1;
}

message GetAllResponse {
	repeated Overlay overlays = 1;
}

message DeleteRequest {
	string id = 1;
}

message CreateLayer {
	OverlayLayerType type = 1;
	OverlayLayerSettings settings = 2;
	int32 pos_x = 3;
	int32 pos_y = 4;
	int32 width = 5;
	int32 height = 6;
	bool periodically_refetch_data = 7;
}

message CreateRequest {
	string name = 1;
	repeated CreateLayer layers = 2;
	int32 width = 3;
	int32 height = 4;
}

message UpdateRequest {
	string id = 1;
	string name = 2;
	repeated CreateLayer layers = 3;
	int32 width = 4;
	int32 height = 5;
}

message ParseHtmlOverlayRequest {
	string html = 1;
}

message ParseHtmlOverlayResponse {
	string html = 1;
}
