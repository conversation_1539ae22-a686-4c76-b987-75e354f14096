syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/api/messages/overlays_kappagen";
package messages.overlays_kappagen;


import "messages/events/events.proto";

enum EmojiStyle {
	None = 0;
	Twemoji = 1;
	Openmoji = 2;
	Noto = 3;
	Blobmoji = 4;
}

message Settings {
	message Emotes {
		int32 time = 1;
		int32 max = 2;
		int32 queue = 3;
		bool ffz_enabled = 4;
		bool bttv_enabled = 5;
		bool seven_tv_enabled = 6;
		EmojiStyle emoji_style = 7;
	}

	message Size {
		double ratio_normal = 1;
		double ratio_small = 2;
		int32 min = 3;
		int32 max = 4;
	}

	message Cube {
		int32 speed = 1;
	}

	message Animation {
		bool fade_in = 1;
		bool fade_out = 2;
		bool zoom_in = 3;
		bool zoom_out = 4;
	}

	message AnimationSettings {
		message Prefs {
			optional double size = 1;
			optional bool center = 2;
			optional int32 speed = 3;
			optional bool faces = 4;
			repeated string message = 5;
			optional int32 time = 6;
		}

		string style = 1;
		Prefs prefs = 2;
		optional int32 count = 3;
		bool enabled = 4;
	}

	message Event {
		events.TwirEventType event = 1;
		repeated string disabled_styles = 2;
		bool enabled = 3;
	}

	Emotes emotes = 1;
	Size size = 2;
	Cube cube = 3;
	Animation animation = 4;
	repeated AnimationSettings animations = 5;
	bool enableRave = 6;
	repeated Event events = 7;
	bool enable_spawn = 8;
	repeated string excluded_emotes = 9;
}
