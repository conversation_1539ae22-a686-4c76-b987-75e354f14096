syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/api/messages/events";
package messages.events;

import "messages/meta/meta.proto";

enum TwirEventType {
	FOLLOW = 0;
	SUBSCRIBE = 1;
	RESUBSCRIBE = 2;
	SUB_GIFT = 3;
	REDEMPTION_CREATED = 4;
	COMMAND_USED = 5;
	FIRST_USER_MESSAGE = 6;
	RAIDED = 7;
	TITLE_OR_CATEGORY_CHANGED = 8;
	STREAM_ONLINE = 9;
	STREAM_OFFLINE = 10;
	CHAT_CLEAR = 11;
	DONATE = 12;
	KEYWORD_USED = 13;
	GREETING_SENDED = 14;
	POLL_STARTED = 15;
	POLL_ENDED = 16;
	POLL_VOTED = 17;
	PREDICTION_STARTED = 18;
	PREDICTION_ENDED = 19;
	PREDICTION_VOTED = 20;
	PREDICTION_LOCKED = 21;
	USER_BANNED = 22;
	CHANNEL_UNBAN_REQUEST_CREATED = 23;
	CHANNEL_UNBAN_REQUEST_RESOLVED = 24;
	CHANNEL_MESSAGE_DELETE = 25;
}

message Event {
	message OperationFilter {
		string type = 1;
		string left = 2;
		string right = 3;
	}

	message Operation {
		string type = 1;
		optional string input = 2;
		uint32 delay = 3;
		uint32 repeat = 4;
		bool use_announce = 5;
		uint32 timeout_time = 6;
		optional string timeout_message = 7;
		optional string target = 8;
		repeated OperationFilter filters = 9;
		bool enabled = 10;
	}

	string id = 1;
	string channel_id = 2;
	string type = 3;
	optional string reward_id = 4;
	optional string command_id = 5;
	optional string keyword_id = 6;
	string description = 7;
	bool enabled = 8;
	bool online_only = 9;
	repeated Operation operations = 10;
}

message GetAllResponse {
	repeated Event events = 1;
}

message GetByIdRequest {
	string id = 1;
}

message CreateRequest {
	Event event = 1;
}

message PutRequest {
	string id = 1;
	Event event = 2;
}

message DeleteRequest {
	string id = 1;
}

message PatchRequest {
	string id = 1;
	bool enabled = 2;
}
