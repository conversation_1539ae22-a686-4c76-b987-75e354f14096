syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/api/messages/integrations_discord";
package messages.integrations_discord;

message GetAuthLink {
	string link = 1;
}

message PostCodeRequest {
	string code = 1;
}

message DiscordGuild {
	string id = 1;
	string name = 2;
	string icon = 3;

	bool live_notification_enabled = 4;
	repeated string live_notification_channels_ids = 5;
	bool live_notification_show_title = 6;
	bool live_notification_show_category = 7;
	string live_notification_message = 8;
	repeated string live_notification_additional_twitch_users_ids = 9;

	repeated GuildChannel channels = 10;
	repeated GuildRole roles = 11;

	string offline_notification_message = 12;
	bool live_notification_show_viewers = 13;
	bool live_notification_show_preview = 14;
	bool live_notification_show_profile_image = 15;
	bool should_delete_message_on_offline = 16;
	repeated string additional_users_ids_for_live_check = 17;
}

message GetDataResponse {
	repeated DiscordGuild guilds = 1;
}

message UpdateMessage {
	repeated DiscordGuild guilds = 1;
}

message DisconnectGuildMessage {
	string guild_id = 1;
}

enum ChannelType {
	VOICE = 0;
	TEXT = 1;
}

message GuildChannel {
	string id = 1;
	string name = 2;
	ChannelType type = 3;
	bool can_send_messages = 4;
}

message GuildRole {
	string id = 1;
	string name = 2;
	string color = 3;
}

message GetGuildChannelsRequest {
	string guild_id = 1;
}

message GetGuildChannelsResponse {
	repeated GuildChannel channels = 1;
}

message GetGuildInfoRequest {
	string guild_id = 1;
}

message GetGuildInfoResponse {
	string id = 1;
	string name = 2;
	string icon = 3;
	repeated GuildChannel channels = 4;
	repeated GuildRole roles = 5;
}
