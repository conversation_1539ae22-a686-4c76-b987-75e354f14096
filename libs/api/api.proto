syntax = "proto3";
option go_package = "github.com/twirapp/twir/libs/api";

package api;

import "google/protobuf/empty.proto";
import "messages/meta/meta.proto";

import "messages/events/events.proto";

import "messages/integrations_donate_stream/integrations_donate_stream.proto";
import "messages/integrations_donatello/integrations_donatello.proto";
import "messages/integrations_donatepay/integrations_donatepay.proto";
import "messages/integrations_donationalerts/integrations_donationalerts.proto";
import "messages/integrations_faceit/integrations_faceit.proto";
import "messages/integrations_lastfm/integrations_lastfm.proto";
import "messages/integrations_streamlabs/integrations_streamlabs.proto";
import "messages/integrations_vk/integrations_vk.proto";
import "messages/integrations_valorant/integrations_valorant.proto";
import "messages/integrations_discord/integrations_discord.proto";
import "messages/integrations_nightbot/integrations_nightbot.proto";

import "messages/modules_obs_websocket/modules_obs_websocket.proto";
import "messages/modules_tts/modules_tts.proto";
import "messages/overlays_kappagen/overlays_kappagen.proto";
import "messages/overlays_be_right_back/overlays_be_right_back.proto";
import "messages/overlays_dudes/overlays_dudes.proto";

import "messages/twitch_protected/twitch_protected.proto";
import "messages/overlays/overlays.proto";

service Protected {
	rpc EventsGetAll(google.protobuf.Empty) returns (messages.events.GetAllResponse) {}
	rpc EventsGetById(messages.events.GetByIdRequest) returns (messages.events.Event) {}
	rpc EventsCreate(messages.events.CreateRequest) returns (messages.events.Event) {}
	rpc EventsDelete(messages.events.DeleteRequest) returns (google.protobuf.Empty) {}
	rpc EventsUpdate(messages.events.PutRequest) returns (messages.events.Event) {}
	rpc EventsEnableOrDisable(messages.events.PatchRequest) returns (messages.events.Event) {}

	rpc IntegrationsDonateStreamGet(google.protobuf.Empty) returns (messages.integrations_donate_stream.DonateStreamResponse) {}
	rpc IntegrationsDonateStreamPostSecret(messages.integrations_donate_stream.DonateStreamPostSecretRequest) returns (google.protobuf.Empty) {}

	rpc IntegrationsDonatelloGet(google.protobuf.Empty) returns (messages.integrations_donatello.GetResponse) {}

	rpc IntegrationsDonatepayGet(google.protobuf.Empty) returns (messages.integrations_donatepay.GetResponse) {}
	rpc IntegrationsDonatepayPut(messages.integrations_donatepay.PostRequest) returns (google.protobuf.Empty) {}

	rpc IntegrationsDonationAlertsGetAuthLink(google.protobuf.Empty) returns (messages.integrations_donationalerts.GetAuthLink) {}
	rpc IntegrationsDonationAlertsGetData(google.protobuf.Empty) returns (messages.integrations_donationalerts.GetDataResponse) {}
	rpc IntegrationsDonationAlertsPostCode(messages.integrations_donationalerts.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsDonationAlertsLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}

	rpc IntegrationsFaceitGetAuthLink(google.protobuf.Empty) returns (messages.integrations_faceit.GetAuthLink) {}
	rpc IntegrationsFaceitGetData(google.protobuf.Empty) returns (messages.integrations_faceit.GetDataResponse) {}
	rpc IntegrationsFaceitUpdate(messages.integrations_faceit.UpdateDataRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsFaceitPostCode(messages.integrations_faceit.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsFaceitLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}

	rpc IntegrationsLastFMGetAuthLink(google.protobuf.Empty) returns (messages.integrations_lastfm.GetAuthLink) {}
	rpc IntegrationsLastFMGetData(google.protobuf.Empty) returns (messages.integrations_lastfm.GetDataResponse) {}
	rpc IntegrationsLastFMPostCode(messages.integrations_lastfm.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsLastFMLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}

	rpc IntegrationsStreamlabsGetAuthLink(google.protobuf.Empty) returns (messages.integrations_streamlabs.GetAuthLink) {}
	rpc IntegrationsStreamlabsGetData(google.protobuf.Empty) returns (messages.integrations_streamlabs.GetDataResponse) {}
	rpc IntegrationsStreamlabsPostCode(messages.integrations_streamlabs.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsStreamlabsLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}

	rpc IntegrationsNightbotGetAuthLink(google.protobuf.Empty) returns (messages.integrations_nightbot.GetAuthLink) {}
	rpc IntegrationsNightbotGetData(google.protobuf.Empty) returns (messages.integrations_nightbot.GetDataResponse) {}
	rpc IntegrationsNightbotPostCode(messages.integrations_nightbot.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsNightbotLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}
	rpc IntegrationsNightbotImportCommands(google.protobuf.Empty) returns (messages.integrations_nightbot.ImportCommandsResponse) {}
	rpc IntegrationsNightbotImportTimers(google.protobuf.Empty) returns (messages.integrations_nightbot.ImportTimersResponse) {}

	rpc IntegrationsVKGetAuthLink(google.protobuf.Empty) returns (messages.integrations_vk.GetAuthLink) {}
	rpc IntegrationsVKGetData(google.protobuf.Empty) returns (messages.integrations_vk.GetDataResponse) {}
	rpc IntegrationsVKPostCode(messages.integrations_vk.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsVKLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}

	rpc IntegrationsDiscordGetAuthLink(google.protobuf.Empty) returns (messages.integrations_discord.GetAuthLink) {}
	rpc IntegrationsDiscordGetData(google.protobuf.Empty) returns (messages.integrations_discord.GetDataResponse) {}
	rpc IntegrationsDiscordUpdate(messages.integrations_discord.UpdateMessage) returns (google.protobuf.Empty) {}
	rpc IntegrationDiscordConnectGuild(messages.integrations_discord.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsDiscordDisconnectGuild(messages.integrations_discord.DisconnectGuildMessage) returns (google.protobuf.Empty) {}
	rpc IntegrationsDiscordGetGuildChannels(messages.integrations_discord.GetGuildChannelsRequest) returns (messages.integrations_discord.GetGuildChannelsResponse) {}
	rpc IntegrationsDiscordGetGuildInfo(messages.integrations_discord.GetGuildInfoRequest) returns (messages.integrations_discord.GetGuildInfoResponse) {}

	rpc ModulesOBSWebsocketGet(google.protobuf.Empty) returns (messages.modules_obs_websocket.GetResponse) {}
	rpc ModulesOBSWebsocketUpdate(messages.modules_obs_websocket.PostRequest) returns (google.protobuf.Empty) {}

	rpc ModulesTTSGet(google.protobuf.Empty) returns (messages.modules_tts.GetResponse) {}
	rpc ModulesTTSUpdate(messages.modules_tts.PostRequest) returns (google.protobuf.Empty) {}
	rpc ModulesTTSGetInfo(google.protobuf.Empty) returns (messages.modules_tts.GetInfoResponse) {}
	rpc ModulesTTSGetUsersSettings(google.protobuf.Empty) returns (messages.modules_tts.GetUsersSettingsResponse) {}
	rpc ModulesTTSUsersDelete(messages.modules_tts.UsersDeleteRequest) returns (google.protobuf.Empty) {}

	rpc OverlayBeRightBackGet(google.protobuf.Empty) returns (messages.overlays_be_right_back.Settings) {}
	rpc OverlayBeRightBackUpdate(messages.overlays_be_right_back.Settings) returns (messages.overlays_be_right_back.Settings) {}

	rpc OverlayKappaGenGet(google.protobuf.Empty) returns (messages.overlays_kappagen.Settings) {}
	rpc OverlayKappaGenUpdate(messages.overlays_kappagen.Settings) returns (messages.overlays_kappagen.Settings) {}

	rpc TwitchSearchCategories(messages.twitch_protected.SearchCategoriesRequest) returns (messages.twitch_protected.SearchCategoriesResponse) {}
	rpc TwitchGetCategories(messages.twitch_protected.GetCategoriesRequest) returns (messages.twitch_protected.SearchCategoriesResponse) {}
	rpc TwitchSetChannelInformation(messages.twitch_protected.SetChannelInformationRequest) returns (google.protobuf.Empty) {}

	rpc OverlaysGetAll(google.protobuf.Empty) returns (messages.overlays.GetAllResponse) {}
	rpc OverlaysGetOne(messages.overlays.GetByIdRequest) returns (messages.overlays.Overlay) {}
	rpc OverlaysUpdate(messages.overlays.UpdateRequest) returns (messages.overlays.Overlay) {}
	rpc OverlaysDelete(messages.overlays.DeleteRequest) returns (google.protobuf.Empty) {}
	rpc OverlaysCreate(messages.overlays.CreateRequest) returns (messages.overlays.Overlay) {}
	rpc OverlaysParseHtml(messages.overlays.ParseHtmlOverlayRequest) returns (messages.overlays.ParseHtmlOverlayResponse) {}

	rpc OverlayDudesGet(messages.overlays_dudes.GetRequest) returns (messages.overlays_dudes.Settings) {}
	rpc OverlayDudesGetAll(google.protobuf.Empty) returns (messages.overlays_dudes.GetAllResponse) {}
	rpc OverlayDudesCreate(messages.overlays_dudes.Settings) returns (messages.overlays_dudes.Settings) {}
	rpc OverlayDudesUpdate(messages.overlays_dudes.UpdateRequest) returns (messages.overlays_dudes.Settings) {}
	rpc OverlayDudesDelete(messages.overlays_dudes.DeleteRequest) returns (google.protobuf.Empty) {}

	rpc IntegrationsValorantGetAuthLink(google.protobuf.Empty) returns (messages.integrations_valorant.GetAuthLink) {}
	rpc IntegrationsValorantGetData(google.protobuf.Empty) returns (messages.integrations_valorant.GetDataResponse) {}
	rpc IntegrationsValorantPostCode(messages.integrations_valorant.PostCodeRequest) returns (google.protobuf.Empty) {}
	rpc IntegrationsValorantLogout(google.protobuf.Empty) returns (google.protobuf.Empty) {}
}

// UnProtected
import "messages/twitch/twitch.proto";
import "messages/songs_unprotected/songs_unprotected.proto";

service UnProtected {
	rpc TwitchGetUsers(messages.twitch.TwitchGetUsersRequest) returns (messages.twitch.TwitchGetUsersResponse) {}
	rpc TwitchSearchChannels(messages.twitch.TwitchSearchChannelsRequest) returns (messages.twitch.TwitchSearchChannelsResponse) {}

	rpc ModulesTTSSay(messages.modules_tts.SayRequest) returns (messages.modules_tts.SayResponse) {}
}
