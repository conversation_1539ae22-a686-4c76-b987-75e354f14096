Icons:
Always use lucide-vue-next icons when possible
Only use other icon libraries/local files when a specific icon is not available in lucide

Vue imports:
Always include .vue extension when importing Vue components
Example: import MyComponent from './MyComponent.vue'

Vue development:
Use Composition API with TypeScript
Use <script setup lang="ts"> for component definitions
Use  defineProps,  defineEmits, and  defineSlots with proper TypeScript types
I already have delete confirmation component, do not try to recreate it from scratch

Forms:
For forms use vee-validate with hook. When using this hook, you should use base html <form>, not <Form> component
For formfields use v-slot="{ componentField }" bindings
Do not forget use FormMessage component
For switches, checkboxes use v-slot="{ value, handleChange }"

Styling:
Use Tailwind CSS for styling
Follow the project's existing Tailwind configuration and color schemes
