package bus_listener

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/nicklaw5/helix/v2"
	cfg "github.com/satont/twir/libs/config"
	"github.com/satont/twir/libs/crypto"
	model "github.com/satont/twir/libs/gomodels"
	"github.com/satont/twir/libs/logger"
	buscore "github.com/twirapp/twir/libs/bus-core"
	"github.com/twirapp/twir/libs/bus-core/tokens"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

var appTokenScopes = []string{}

type appToken struct {
	AccessToken    string
	ObtainmentTime time.Time
	ExpiresIn      int
}

type Opts struct {
	fx.In
	Lc fx.Lifecycle

	Config  cfg.Config
	Gorm    *gorm.DB
	Redsync *redsync.Redsync
	Logger  logger.Logger
	Bus     *buscore.Bus
}

type BusListener struct {
	globalClient   *helix.Client
	appAccessToken *appToken

	db      *gorm.DB
	config  cfg.Config
	log     logger.Logger
	redSync *redsync.Redsync
	bus     *buscore.Bus
}

const expireShift = 15 * time.Minute

func isTokenExpired(expiresIn int, obtainmentTimestamp time.Time) bool {
	currentTime := time.Now().UTC()
	currentTokenLiveTime := currentTime.Sub(obtainmentTimestamp.UTC())

	return int64(currentTokenLiveTime.Seconds())+int64(expireShift.Seconds()) >= int64(expiresIn)
}

func rateLimitFunc(lastResponse *helix.Response) error {
	if lastResponse.GetRateLimitRemaining() > 0 {
		return nil
	}

	var reset64 int64
	reset64 = int64(lastResponse.GetRateLimitReset())

	currentTime := time.Now().UTC().Unix()

	if currentTime < reset64 {
		timeDiff := time.Duration(reset64 - currentTime)
		if timeDiff > 0 {
			time.Sleep(timeDiff * time.Second)
		}
	}

	return nil
}

func New(opts Opts) (*BusListener, error) {
	helixClient, err := helix.NewClient(
		&helix.Options{
			ClientID:      opts.Config.TwitchClientId,
			ClientSecret:  opts.Config.TwitchClientSecret,
			RedirectURI:   opts.Config.GetTwitchCallbackUrl(),
			RateLimitFunc: rateLimitFunc,
		},
	)
	if err != nil {
		return nil, err
	}
	appAccessToken, err := helixClient.RequestAppAccessToken(appTokenScopes)
	if err != nil {
		return nil, err
	}

	listener := &BusListener{
		globalClient: helixClient,
		appAccessToken: &appToken{
			AccessToken:    appAccessToken.Data.AccessToken,
			ObtainmentTime: time.Now().UTC(),
			ExpiresIn:      appAccessToken.Data.ExpiresIn,
		},

		db:      opts.Gorm,
		config:  opts.Config,
		log:     opts.Logger,
		redSync: opts.Redsync,
		bus:     opts.Bus,
	}

	opts.Bus.Tokens.RequestAppToken.SubscribeGroup("tokens", listener.handleRequestAppToken)
	opts.Bus.Tokens.RequestUserToken.SubscribeGroup("tokens", listener.handleRequestUserToken)
	opts.Bus.Tokens.RequestBotToken.SubscribeGroup("tokens", listener.handleRequestBotToken)
	opts.Bus.Tokens.UpdateBotToken.SubscribeGroup("tokens", listener.handleUpdateBotToken)
	opts.Bus.Tokens.UpdateUserToken.SubscribeGroup("tokens", listener.handleUpdateUserToken)

	opts.Lc.Append(
		fx.Hook{
			OnStart: func(ctx context.Context) error {
				opts.Logger.Info("Tokens bus listener started")
				return nil
			},
			OnStop: func(ctx context.Context) error {
				opts.Bus.Tokens.RequestAppToken.Unsubscribe()
				opts.Bus.Tokens.RequestUserToken.Unsubscribe()
				opts.Bus.Tokens.RequestBotToken.Unsubscribe()
				opts.Bus.Tokens.UpdateBotToken.Unsubscribe()
				opts.Bus.Tokens.UpdateUserToken.Unsubscribe()
				return nil
			},
		},
	)

	return listener, nil
}

func (c *BusListener) handleRequestAppToken(
	ctx context.Context,
	_ struct{},
) (*tokens.Token, error) {
	mu := c.redSync.NewMutex("tokens-app-lock")
	mu.Lock()
	defer mu.Unlock()

	if isTokenExpired(c.appAccessToken.ExpiresIn, c.appAccessToken.ObtainmentTime) {
		appAccessToken, err := c.globalClient.RequestAppAccessToken(appTokenScopes)
		if err != nil {
			return nil, err
		}

		c.appAccessToken = &appToken{
			AccessToken:    appAccessToken.Data.AccessToken,
			ObtainmentTime: time.Now().UTC(),
			ExpiresIn:      appAccessToken.Data.ExpiresIn,
		}
		c.log.Info("app token refreshed")
	}

	return &tokens.Token{
		AccessToken: c.appAccessToken.AccessToken,
		Scopes:      []string{},
	}, nil
}

func (c *BusListener) handleRequestUserToken(
	ctx context.Context,
	data tokens.GetUserTokenRequest,
) (*tokens.Token, error) {
	mu := c.redSync.NewMutex("tokens-users-lock-" + data.UserID)
	mu.Lock()
	defer mu.Unlock()

	user := model.Users{}
	err := c.db.WithContext(ctx).Where("id = ?", data.UserID).Preload("Token").Find(&user).Error
	if err != nil {
		return nil, err
	}

	if user.ID == "" || user.Token == nil || user.Token.ID == "" {
		return nil, fmt.Errorf(
			"cannot find user token in db, userId: %s, token: %v",
			user.ID,
			user.Token,
		)
	}

	decryptedRefreshToken, err := crypto.Decrypt(user.Token.RefreshToken, c.config.TokensCipherKey)
	if err != nil {
		return nil, err
	}

	if decryptedRefreshToken == "" {
		return nil, errors.New("refresh token is empty")
	}

	if isTokenExpired(int(user.Token.ExpiresIn), user.Token.ObtainmentTimestamp) {
		newToken, err := c.globalClient.RefreshUserAccessToken(decryptedRefreshToken)
		if err != nil {
			return nil, err
		}
		if newToken.ErrorMessage != "" {
			return nil, fmt.Errorf("refresh token error: %s", newToken.ErrorMessage)
		}

		if newToken.StatusCode != 200 || newToken.Data.AccessToken == "" {
			return nil, fmt.Errorf("refresh token status code: %d", newToken.StatusCode)
		}

		newRefreshToken, err := crypto.Encrypt(newToken.Data.RefreshToken, c.config.TokensCipherKey)
		if err != nil {
			return nil, err
		}
		user.Token.RefreshToken = newRefreshToken

		newAccessToken, err := crypto.Encrypt(newToken.Data.AccessToken, c.config.TokensCipherKey)
		if err != nil {
			return nil, err
		}
		user.Token.AccessToken = newAccessToken

		user.Token.ExpiresIn = int32(newToken.Data.ExpiresIn)
		user.Token.Scopes = newToken.Data.Scopes
		user.Token.ObtainmentTimestamp = time.Now().UTC()
		if err := c.db.WithContext(ctx).Save(&user.Token).Error; err != nil {
			return nil, err
		}

		c.log.Info(
			"user token refreshed",
			slog.String("user_id", user.ID),
			slog.Int("expires_in", int(user.Token.ExpiresIn)),
			slog.String("access_token", newAccessToken),
			slog.String("refresh_token", newRefreshToken),
		)
	}

	decryptedAccessToken, err := crypto.Decrypt(user.Token.AccessToken, c.config.TokensCipherKey)
	if err != nil {
		return nil, err
	}

	return &tokens.Token{
		AccessToken: decryptedAccessToken,
		Scopes:      user.Token.Scopes,
	}, nil
}

func (c *BusListener) handleRequestBotToken(
	ctx context.Context,
	data tokens.GetBotTokenRequest,
) (*tokens.Token, error) {
	mu := c.redSync.NewMutex("tokens-bots-lock-" + data.BotID)
	mu.Lock()
	defer mu.Unlock()

	bot := model.Bots{}
	err := c.db.WithContext(ctx).Where("id = ?", data.BotID).Preload("Token").Find(&bot).Error
	if err != nil {
		return nil, err
	}

	if bot.ID == "" || bot.Token == nil || bot.Token.ID == "" {
		return nil, errors.New("cannot find bot token in db")
	}

	decryptedRefreshToken, err := crypto.Decrypt(bot.Token.RefreshToken, c.config.TokensCipherKey)
	if err != nil {
		return nil, err
	}

	if isTokenExpired(int(bot.Token.ExpiresIn), bot.Token.ObtainmentTimestamp) {
		newToken, err := c.globalClient.RefreshUserAccessToken(decryptedRefreshToken)
		if err != nil {
			return nil, err
		}

		if newToken.ErrorMessage != "" {
			return nil, fmt.Errorf("refresh token error: %s", newToken.ErrorMessage)
		}

		newRefreshToken, err := crypto.Encrypt(newToken.Data.RefreshToken, c.config.TokensCipherKey)
		if err != nil {
			return nil, err
		}
		bot.Token.RefreshToken = newRefreshToken

		newAccessToken, err := crypto.Encrypt(newToken.Data.AccessToken, c.config.TokensCipherKey)
		if err != nil {
			return nil, err
		}
		bot.Token.AccessToken = newAccessToken

		bot.Token.ExpiresIn = int32(newToken.Data.ExpiresIn)
		bot.Token.Scopes = newToken.Data.Scopes
		bot.Token.ObtainmentTimestamp = time.Now().UTC()
		if err := c.db.WithContext(ctx).Save(&bot.Token).Error; err != nil {
			return nil, err
		}
		c.log.Info("bot token refreshed", slog.String("bot_id", bot.ID))
	}

	decryptedAccessToken, err := crypto.Decrypt(bot.Token.AccessToken, c.config.TokensCipherKey)
	if err != nil {
		return nil, err
	}

	return &tokens.Token{
		AccessToken: decryptedAccessToken,
		Scopes:      bot.Token.Scopes,
		ExpiresIn:   bot.Token.ExpiresIn,
	}, nil
}

func (c *BusListener) handleUpdateBotToken(
	ctx context.Context,
	data tokens.UpdateTokenRequest,
) (*struct{}, error) {
	// Implementation for updating bot token
	// This would need to be implemented based on your business logic
	return &struct{}{}, nil
}

func (c *BusListener) handleUpdateUserToken(
	ctx context.Context,
	data tokens.UpdateTokenRequest,
) (*struct{}, error) {
	// Implementation for updating user token
	// This would need to be implemented based on your business logic
	return &struct{}{}, nil
}
