{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "module": "ESNext",
    "target": "ESNext",
    "moduleResolution": "bundler",
    "noImplicitAny": true,
    "allowImportingTsExtensions": true,
    "sourceMap": true,
    "inlineSources": true,
    "strict": true,
    "strictNullChecks": true,
    "noUncheckedIndexedAccess": true,
    "outDir": "./dist",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "declaration": true,
    "importHelpers": true,
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strictPropertyInitialization": false,
    "noEmit": true,
  },
  "exclude": [
    "node_modules",
    "dist",
  ],
  "include": [
    "src"
  ]
}
