{"name": "twir", "author": "Satont <<EMAIL>>", "devDependencies": {"@antfu/eslint-config": "2.14.0", "@types/bun": "catalog:", "@types/node": "20.12.7", "@vue/language-server": "2.2.8", "rimraf": "5.0.5", "typescript": "catalog:"}, "engines": {"bun": ">=1.2.15"}, "private": true, "scripts": {"dev": "bun run cli dev", "build": "bun run cli build", "cli": "go run ./cli/main.go", "lint": "eslint ./frontend", "lint:fix": "eslint . --fix"}, "trustedDependencies": ["@parcel/watcher", "@twir/web", "core-js", "esbuild", "protobufjs", "sharp", "vue-demi"], "type": "module", "workspaces": {"packages": ["libs/*", "apps/*", "frontend/*", "web"], "catalog": {"vue": "3.5.13", "vue-router": "4.3.0", "vue-tsc": "2.2.10", "vite": "5.4.6", "typescript": "5.8.2", "graphql": "16.10.0", "graphql-ws": "6.0.4", "@types/bun": "1.2.15", "@vueuse/components": "13.3.0", "@vueuse/core": "13.3.0", "@vueuse/router": "13.3.0"}}}