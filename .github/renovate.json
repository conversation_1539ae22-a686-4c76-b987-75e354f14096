{"repositories": ["sat<PERSON>/tsuwari"], "extends": ["config:base", ":disableRateLimiting", "group:all"], "rebaseWhen": "conflicted", "commitBody": "[skip-docker]", "gitAuthor": "Renovate Bot <<EMAIL>>", "semanticCommits": true, "semanticPrefix": "chore(dependency)", "separateMajorMinor": false, "labels": ["dependencies"], "lockFileMaintenance": {"enabled": true}, "baseBranches": ["main"], "packageFiles": ["package.json", "apps/**/package.json", "libs/**/package.json", ".github/workflows/**/*.yml"]}