name: Docker v2

#on:
#  push:
#    branches:
#      - main
#  workflow_dispatch:

jobs:
  base:
    runs-on: self-hosted
    strategy:
      matrix:
        target:
          - builder
          - node_prod_base
          - go_prod_base
    steps:
      - uses: TooMuch4U/actions-clean@v2.1
      - uses: actions/checkout@v3
        with:
          submodules: true

      - run: |
          docker build -f ./base.Dockerfile \
          -t twir-base:${{ matrix.target }} \
          --target ${{ matrix.target }} \
          --cache-from type=gha,scope=twir-base:${{ matrix.target }} \
          --cache-to type=gha,mode=max,scope=twir-base:${{ matrix.target }} \
          .

  backend:
    runs-on: self-hosted
    needs:
      - base
    strategy:
      matrix:
        target:
          - api
          - bots
          - discord
          - emotes-cacher
          - eval
          - events
          - eventsub
          - integrations
          - parser
          - scheduler
          - timers
          - tokens
          - websockets
          - ytsr
    steps:
      - uses: TooMuch4U/actions-clean@v2.1
      - uses: actions/checkout@v3

      - run: |
          docker build -f ./apps/${{ matrix.target }}/Dockerfile \
          -t twirapp/${{ matrix.target }}:latest .

          docker push twirapp/${{ matrix.target }}:latest

  frontend:
    runs-on: self-hosted
    needs:
      - base
    strategy:
      matrix:
        target:
          - dashboard
          - landing
          - overlays
          - public-page
    steps:
      - uses: TooMuch4U/actions-clean@v2.1
      - uses: actions/checkout@v3

      - run: |
          docker build -f ./frontend/${{ matrix.target }}/Dockerfile \
          -t twirapp/${{ matrix.target }}:latest .

          docker push twirapp/${{ matrix.target }}:latest

  migrations:
    runs-on: self-hosted
    needs:
      - base
    steps:
      - uses: TooMuch4U/actions-clean@v2.1
      - uses: actions/checkout@v3

      - run: |
          docker build -f ./libs/migrations/Dockerfile \
          -t twirapp/migrations:latest \
          --cache-from type=gha,scope=twir-migrations \
          --cache-to type=gha,mode=max,scope=twir-migrations .

          docker push twirapp/migrations:latest
